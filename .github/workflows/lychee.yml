name: <PERSON> (lychee)
on:
  pull_request:
    paths:
      - 'docs/**'
      - 'PRPs/**'
      - 'README.md'
      - 'PLANNING.md'
jobs:
  lychee:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Install lychee
        uses: lycheeverse/lychee-action@v1.10.0
        with:
          args: --config .lychee.toml --verbose --no-progress "docs/**/*.md" "PRPs/**/*.md" README.md PLANNING.md
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}


