name: Validate Platform Status
on:
  pull_request:
    paths:
      - 'docs/platform/status.yaml'
      - 'docs/platform/status.schema.json'
      - 'tools/status-validator/**'
jobs:
  validate-status:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - name: Install deps
        run: pip3 install pyyaml jsonschema
      - name: Run status validator
        run: python3 tools/status-validator/validate_status.py


