name: Marketplace CI

"on":
  push:
    branches: [main, develop]
    paths:
      - 'services/marketplace/**'
      - '.github/workflows/marketplace-ci.yml'
      - '.github/workflows/ci-common.yml'
      - '.github/actions/setup-go/**'
  pull_request:
    branches: [main, develop]
    paths:
      - 'services/marketplace/**'
      - '.github/workflows/marketplace-ci.yml'
      - '.github/workflows/ci-common.yml'
      - '.github/actions/setup-go/**'

permissions:
  contents: read
  security-events: write
  actions: read
  checks: write
  pull-requests: write
  deployments: write

jobs:
  ci:
    uses: ./.github/workflows/ci-common.yml
    with:
      service_name: marketplace
      language: go
      working_directory: services/marketplace
    secrets:
      GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  deploy-dev:
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    needs: ci
    uses: ./.github/workflows/cd-deploy.yml
    with:
      service_name: marketplace
      environment: development
      version: ${{ github.sha }}
      rollout_strategy: rolling
    secrets:
      GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}

  deploy-staging:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: ci
    uses: ./.github/workflows/cd-deploy.yml
    with:
      service_name: marketplace
      environment: staging
      version: ${{ github.sha }}
      rollout_strategy: blue-green
    secrets:
      GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
