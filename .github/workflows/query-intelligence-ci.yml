name: Query Intelligence CI

"on":
  push:
    branches: [main, develop]
    paths:
      - 'services/query-intelligence/**'
      - '.github/workflows/query-intelligence-ci.yml'
      - '.github/workflows/ci-common.yml'
      - '.github/actions/setup-python/**'
  pull_request:
    branches: [main, develop]
    paths:
      - 'services/query-intelligence/**'
      - '.github/workflows/query-intelligence-ci.yml'
      - '.github/workflows/ci-common.yml'
      - '.github/actions/setup-python/**'

permissions:
  contents: read
  security-events: write
  actions: read
  checks: write
  pull-requests: write
  deployments: write

jobs:
  ci:
    uses: ./.github/workflows/ci-common.yml
    with:
      service_name: query-intelligence
      language: python
      working_directory: services/query-intelligence
    secrets:
      GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  deploy-dev:
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    needs: ci
    uses: ./.github/workflows/cd-deploy.yml
    with:
      service_name: query-intelligence
      environment: development
      version: ${{ github.sha }}
      rollout_strategy: rolling
    secrets:
      GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}

  deploy-staging:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: ci
    uses: ./.github/workflows/cd-deploy.yml
    with:
      service_name: query-intelligence
      environment: staging
      version: ${{ github.sha }}
      rollout_strategy: canary
      initial_traffic_percentage: 10
    secrets:
      GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
