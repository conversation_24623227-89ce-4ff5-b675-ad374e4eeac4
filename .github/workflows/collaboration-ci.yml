name: Collaboration CI

"on":
  push:
    branches: [main, develop]
    paths:
      - 'services/collaboration/**'
      - '.github/workflows/collaboration-ci.yml'
      - '.github/workflows/ci-common.yml'
      - '.github/actions/setup-typescript/**'
  pull_request:
    branches: [main, develop]
    paths:
      - 'services/collaboration/**'
      - '.github/workflows/collaboration-ci.yml'
      - '.github/workflows/ci-common.yml'
      - '.github/actions/setup-typescript/**'

permissions:
  contents: read
  security-events: write
  actions: read
  checks: write
  pull-requests: write
  deployments: write

jobs:
  ci:
    uses: ./.github/workflows/ci-common.yml
    with:
      service_name: collaboration
      language: typescript
      working_directory: services/collaboration
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}