name: Analysis Engine CI

"on":
  push:
    branches: [main, develop]
    paths:
      - 'services/analysis-engine/**'
      - '.github/workflows/analysis-engine-ci.yml'
      - '.github/workflows/ci-common.yml'
      - '.github/actions/setup-rust/**'
  pull_request:
    branches: [main, develop]
    paths:
      - 'services/analysis-engine/**'
      - '.github/workflows/analysis-engine-ci.yml'
      - '.github/workflows/ci-common.yml'
      - '.github/actions/setup-rust/**'

permissions:
  contents: read
  security-events: write
  actions: read
  checks: write
  pull-requests: write
  deployments: write

jobs:
  ci:
    uses: ./.github/workflows/ci-common.yml
    with:
      service_name: analysis-engine
      language: rust
      working_directory: services/analysis-engine
    secrets:
      GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
    secrets: inherit

  deploy-dev:
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    needs: ci
    uses: ./.github/workflows/cd-deploy.yml
    with:
      service_name: analysis-engine
      environment: development
      version: ${{ github.sha }}
      rollout_strategy: rolling
    secrets:
      GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}

  deploy-staging:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: ci
    uses: ./.github/workflows/cd-deploy.yml
    with:
      service_name: analysis-engine
      environment: staging
      version: ${{ github.sha }}
      rollout_strategy: canary
      initial_traffic_percentage: 25
    secrets:
      GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
