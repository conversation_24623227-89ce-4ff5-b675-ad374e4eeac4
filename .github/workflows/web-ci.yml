name: Web CI

"on":
  push:
    branches: [main, develop]
    paths:
      - 'services/web/**'
      - '.github/workflows/web-ci.yml'
      - '.github/workflows/ci-common.yml'
      - '.github/actions/setup-typescript/**'
  pull_request:
    branches: [main, develop]
    paths:
      - 'services/web/**'
      - '.github/workflows/web-ci.yml'
      - '.github/workflows/ci-common.yml'
      - '.github/actions/setup-typescript/**'

permissions:
  contents: read
  security-events: write
  actions: read
  checks: write
  pull-requests: write
  deployments: write

jobs:
  ci:
    uses: ./.github/workflows/ci-common.yml
    with:
      service_name: web
      language: typescript
      working_directory: services/web
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}