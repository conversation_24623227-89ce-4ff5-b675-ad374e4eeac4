name: Deploy to Production

on:
  workflow_dispatch:
    inputs:
      service:
        description: 'Service to deploy'
        required: true
        type: choice
        options:
          - analysis-engine
          - query-intelligence
          - pattern-mining
          - marketplace
          - collaboration
          - web
          - all
      version:
        description: 'Version/Tag to deploy (default: latest)'
        required: false
        default: 'latest'
      environment:
        description: 'Target environment'
        required: true
        type: choice
        options:
          - staging
          - production
        default: 'staging'
      rollback_on_failure:
        description: 'Automatically rollback on deployment failure'
        required: false
        type: boolean
        default: true

env:
  GCP_PROJECT_ID: ccl-platform-prod
  GAR_REGISTRY: us-central1-docker.pkg.dev
  CLUSTER_NAME: ccl-prod-cluster
  CLUSTER_LOCATION: us-central1

jobs:
  validate-deployment:
    name: Validate Deployment Request
    runs-on: ubuntu-latest
    outputs:
      services: ${{ steps.determine-services.outputs.services }}
      version: ${{ steps.validate.outputs.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Validate deployment parameters
        id: validate
        run: |
          # Validate version format
          VERSION="${{ github.event.inputs.version }}"
          if [[ "$VERSION" == "latest" ]]; then
            VERSION=$(git describe --tags --abbrev=0 2>/dev/null || echo "v1.0.0")
          fi
          
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          
          # Validate service selection
          SERVICE="${{ github.event.inputs.service }}"
          if [[ "$SERVICE" == "all" ]]; then
            echo "Deploying all services to ${{ github.event.inputs.environment }}"
          else
            echo "Deploying $SERVICE to ${{ github.event.inputs.environment }}"
          fi

      - name: Determine services to deploy
        id: determine-services
        run: |
          SERVICE="${{ github.event.inputs.service }}"
          if [[ "$SERVICE" == "all" ]]; then
            SERVICES='["analysis-engine", "query-intelligence", "pattern-mining", "marketplace", "collaboration", "web"]'
          else
            SERVICES='["'$SERVICE'"]'
          fi
          echo "services=$SERVICES" >> $GITHUB_OUTPUT

      - name: Check service health in staging
        if: github.event.inputs.environment == 'production'
        run: |
          echo "Checking staging health before production deployment..."
          # Add actual health checks here
          curl -f https://staging-api.ccl.dev/health || exit 1

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: validate-deployment
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.GCP_PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true

      - name: Scan container images
        run: |
          SERVICES='${{ needs.validate-deployment.outputs.services }}'
          VERSION='${{ needs.validate-deployment.outputs.version }}'
          
          echo "$SERVICES" | jq -r '.[]' | while read service; do
            echo "Scanning $service:$VERSION"
            
            # Pull the image
            IMAGE_URL="$GAR_REGISTRY/$GCP_PROJECT_ID/ccl-images/$service:$VERSION"
            docker pull "$IMAGE_URL"
            
            # Run Trivy scan
            docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
              -v $HOME/Library/Caches:/root/.cache/ \
              aquasec/trivy:latest image \
              --severity HIGH,CRITICAL \
              --exit-code 1 \
              "$IMAGE_URL"
          done

  approval-gate:
    name: Production Deployment Approval
    runs-on: ubuntu-latest
    needs: [validate-deployment, security-scan]
    if: github.event.inputs.environment == 'production'
    environment: 
      name: production-approval
      url: https://production.ccl.dev
    steps:
      - name: Request approval for production deployment
        run: |
          echo "Production deployment requires manual approval."
          echo "Service(s): ${{ github.event.inputs.service }}"
          echo "Version: ${{ needs.validate-deployment.outputs.version }}"
          echo "Rollback enabled: ${{ github.event.inputs.rollback_on_failure }}"

  deploy:
    name: Deploy Service
    runs-on: ubuntu-latest
    needs: [validate-deployment, security-scan]
    if: always() && (needs.approval-gate.result == 'success' || github.event.inputs.environment == 'staging')
    strategy:
      matrix:
        service: ${{ fromJson(needs.validate-deployment.outputs.services) }}
      fail-fast: false
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.GCP_PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true

      - name: Configure kubectl
        run: |
          gcloud container clusters get-credentials $CLUSTER_NAME \
            --location $CLUSTER_LOCATION \
            --project $GCP_PROJECT_ID

      - name: Store current deployment for rollback
        id: store-current
        run: |
          CURRENT_IMAGE=$(kubectl get deployment ${{ matrix.service }} \
            -n ${{ github.event.inputs.environment }} \
            -o jsonpath='{.spec.template.spec.containers[0].image}' 2>/dev/null || echo "none")
          echo "current_image=$CURRENT_IMAGE" >> $GITHUB_OUTPUT
          echo "Stored current image: $CURRENT_IMAGE"

      - name: Deploy to Kubernetes
        id: deploy
        run: |
          SERVICE="${{ matrix.service }}"
          VERSION="${{ needs.validate-deployment.outputs.version }}"
          ENVIRONMENT="${{ github.event.inputs.environment }}"
          
          IMAGE_URL="$GAR_REGISTRY/$GCP_PROJECT_ID/ccl-images/$SERVICE:$VERSION"
          
          echo "Deploying $SERVICE:$VERSION to $ENVIRONMENT"
          
          # Update deployment
          kubectl set image deployment/$SERVICE \
            $SERVICE=$IMAGE_URL \
            -n $ENVIRONMENT
          
          # Wait for rollout
          kubectl rollout status deployment/$SERVICE \
            -n $ENVIRONMENT \
            --timeout=600s

      - name: Run smoke tests
        id: smoke-tests
        run: |
          SERVICE="${{ matrix.service }}"
          ENVIRONMENT="${{ github.event.inputs.environment }}"
          
          # Get service URL
          if [[ "$ENVIRONMENT" == "production" ]]; then
            BASE_URL="https://api.ccl.dev"
          else
            BASE_URL="https://staging-api.ccl.dev"
          fi
          
          # Service-specific health checks
          case $SERVICE in
            "analysis-engine")
              curl -f "$BASE_URL/api/v1/analysis/health" || exit 1
              ;;
            "query-intelligence")
              curl -f "$BASE_URL/api/v1/query/health" || exit 1
              ;;
            "pattern-mining")
              curl -f "$BASE_URL/api/v1/patterns/health" || exit 1
              ;;
            "marketplace")
              curl -f "$BASE_URL/api/v1/marketplace/health" || exit 1
              ;;
            "collaboration")
              curl -f "$BASE_URL/api/v1/collaboration/health" || exit 1
              ;;
            "web")
              curl -f "$BASE_URL/health" || exit 1
              ;;
          esac
          
          echo "Smoke tests passed for $SERVICE"

      - name: Rollback on failure
        if: failure() && github.event.inputs.rollback_on_failure == 'true' && steps.store-current.outputs.current_image != 'none'
        run: |
          SERVICE="${{ matrix.service }}"
          ENVIRONMENT="${{ github.event.inputs.environment }}"
          CURRENT_IMAGE="${{ steps.store-current.outputs.current_image }}"
          
          echo "Rolling back $SERVICE to $CURRENT_IMAGE"
          
          kubectl set image deployment/$SERVICE \
            $SERVICE=$CURRENT_IMAGE \
            -n $ENVIRONMENT
          
          kubectl rollout status deployment/$SERVICE \
            -n $ENVIRONMENT \
            --timeout=300s
          
          echo "Rollback completed for $SERVICE"

      - name: Update deployment tracking
        if: success()
        run: |
          # Record successful deployment
          cat >> deployment-log.txt << EOF
          {
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "service": "${{ matrix.service }}",
            "version": "${{ needs.validate-deployment.outputs.version }}",
            "environment": "${{ github.event.inputs.environment }}",
            "actor": "${{ github.actor }}",
            "workflow_run": "${{ github.run_id }}"
          }
          EOF

  notify:
    name: Notify Teams
    runs-on: ubuntu-latest
    needs: [validate-deployment, deploy]
    if: always()
    steps:
      - name: Determine overall status
        id: status
        run: |
          if [[ "${{ needs.deploy.result }}" == "success" ]]; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "color=good" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "color=danger" >> $GITHUB_OUTPUT
          fi

      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ steps.status.outputs.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          custom_payload: |
            {
              text: "Production Deployment ${{ steps.status.outputs.status == 'success' && 'Completed' || 'Failed' }}",
              attachments: [{
                color: "${{ steps.status.outputs.color }}",
                blocks: [{
                  type: "section",
                  text: {
                    type: "mrkdwn",
                    text: "*Service(s):* ${{ github.event.inputs.service }}\n*Version:* ${{ needs.validate-deployment.outputs.version }}\n*Environment:* ${{ github.event.inputs.environment }}\n*Actor:* ${{ github.actor }}"
                  }
                }]
              }]
            }

      - name: Notify PagerDuty on failure
        if: failure() && github.event.inputs.environment == 'production'
        uses: PagerDuty/github-action@v2
        with:
          pagerduty-integration-key: ${{ secrets.PAGERDUTY_INTEGRATION_KEY }}
          pagerduty-dedup-key: deployment-failure-${{ github.run_id }}
          pagerduty-event-action: trigger
          pagerduty-summary: "Production deployment failed for ${{ github.event.inputs.service }}"
          pagerduty-severity: error
          pagerduty-source: github-actions