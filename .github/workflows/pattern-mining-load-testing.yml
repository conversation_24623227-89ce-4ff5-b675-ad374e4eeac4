name: Pattern Mining Phase 2 Load Testing

on:
  # Manual trigger for comprehensive load testing
  workflow_dispatch:
    inputs:
      testing_profile:
        description: 'Testing profile to run'
        required: true
        type: choice
        options:
          - development
          - staging
          - production
          - endurance
        default: 'development'
      test_scenarios:
        description: 'Test scenarios to execute (comma-separated)'
        required: false
        type: string
        default: 'concurrent_users,sustained_load,realistic_workloads'
      max_duration_hours:
        description: 'Maximum test duration in hours'
        required: false
        type: number
        default: 1
      skip_memory_profiling:
        description: 'Skip memory profiling to reduce resource usage'
        required: false
        type: boolean
        default: false

  # Scheduled runs for continuous performance monitoring
  schedule:
    # Run development profile daily at 2 AM UTC
    - cron: '0 2 * * *'
  
  # Trigger on significant changes to performance-critical paths
  push:
    branches: [main]
    paths:
      - 'services/pattern-mining/src/**'
      - 'services/pattern-mining/tests/performance/**'
      - 'services/pattern-mining/config/load_testing_config.yaml'
      - '.github/workflows/pattern-mining-load-testing.yml'

permissions:
  contents: read
  actions: read
  checks: write
  pull-requests: write
  deployments: write

env:
  SERVICE_NAME: pattern-mining
  WORKING_DIR: services/pattern-mining
  PYTHON_VERSION: '3.11'
  LOAD_TEST_TIMEOUT: 14400  # 4 hours max
  
jobs:
  # Pre-flight checks before running expensive load tests
  preflight-checks:
    runs-on: ubuntu-latest
    outputs:
      should_run_tests: ${{ steps.check.outputs.should_run }}
      testing_profile: ${{ steps.profile.outputs.profile }}
      test_scenarios: ${{ steps.scenarios.outputs.scenarios }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Determine testing profile
        id: profile
        run: |
          if [ "${{ github.event_name }}" = "schedule" ]; then
            echo "profile=development" >> $GITHUB_OUTPUT
          elif [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "profile=${{ github.event.inputs.testing_profile }}" >> $GITHUB_OUTPUT
          elif [ "${{ github.event_name }}" = "push" ]; then
            echo "profile=staging" >> $GITHUB_OUTPUT
          else
            echo "profile=development" >> $GITHUB_OUTPUT
          fi

      - name: Determine test scenarios
        id: scenarios
        run: |
          if [ "${{ github.event.inputs.test_scenarios }}" != "" ]; then
            echo "scenarios=${{ github.event.inputs.test_scenarios }}" >> $GITHUB_OUTPUT
          else
            # Default scenarios based on profile
            case "${{ steps.profile.outputs.profile }}" in
              "development")
                echo "scenarios=concurrent_users" >> $GITHUB_OUTPUT
                ;;
              "staging")
                echo "scenarios=concurrent_users,realistic_workloads" >> $GITHUB_OUTPUT
                ;;
              "production"|"endurance")
                echo "scenarios=concurrent_users,sustained_load,realistic_workloads,scalability_limits" >> $GITHUB_OUTPUT
                ;;
              *)
                echo "scenarios=concurrent_users" >> $GITHUB_OUTPUT
                ;;
            esac
          fi

      - name: Check if tests should run
        id: check
        run: |
          # Check if this is a significant change that warrants load testing
          if [ "${{ github.event_name }}" = "push" ]; then
            # Check if changes include performance-critical files
            git diff --name-only HEAD~1 HEAD | grep -E "(src/|tests/performance/|config/)" && echo "should_run=true" >> $GITHUB_OUTPUT || echo "should_run=false" >> $GITHUB_OUTPUT
          else
            echo "should_run=true" >> $GITHUB_OUTPUT
          fi

      - name: Log configuration
        run: |
          echo "Testing Profile: ${{ steps.profile.outputs.profile }}"
          echo "Test Scenarios: ${{ steps.scenarios.outputs.scenarios }}"
          echo "Should Run Tests: ${{ steps.check.outputs.should_run }}"

  # Set up test infrastructure
  setup-infrastructure:
    runs-on: ubuntu-latest
    needs: preflight-checks
    if: needs.preflight-checks.outputs.should_run_tests == 'true'
    outputs:
      infrastructure_ready: ${{ steps.setup.outputs.ready }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: ./.github/actions/setup-python
        with:
          service_name: ${{ env.SERVICE_NAME }}

      - name: Start test infrastructure
        id: setup
        run: |
          cd ${{ env.WORKING_DIR }}
          
          # Start PostgreSQL, Redis, and other dependencies
          docker-compose -f docker-compose.yml up -d postgres redis
          
          # Wait for services to be ready
          echo "Waiting for infrastructure to be ready..."
          timeout 120 bash -c 'until docker-compose exec -T postgres pg_isready; do sleep 2; done'
          timeout 60 bash -c 'until docker-compose exec -T redis redis-cli ping | grep PONG; do sleep 2; done'
          
          # Run database migrations
          make db-upgrade || true
          
          echo "ready=true" >> $GITHUB_OUTPUT

      - name: Verify infrastructure health
        run: |
          cd ${{ env.WORKING_DIR }}
          
          # Test database connectivity
          docker-compose exec -T postgres psql -U postgres -d pattern_mining -c "SELECT 1;" || exit 1
          
          # Test Redis connectivity  
          docker-compose exec -T redis redis-cli ping || exit 1
          
          echo "Infrastructure health check passed!"

  # Phase 2 Concurrent Users Testing
  concurrent-users-testing:
    runs-on: ubuntu-latest
    needs: [preflight-checks, setup-infrastructure]
    if: |
      needs.preflight-checks.outputs.should_run_tests == 'true' && 
      contains(needs.preflight-checks.outputs.test_scenarios, 'concurrent_users')
    timeout-minutes: 240  # 4 hours max
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: ./.github/actions/setup-python
        with:
          service_name: ${{ env.SERVICE_NAME }}

      - name: Install additional dependencies
        run: |
          cd ${{ env.WORKING_DIR }}
          pip install psutil memory_profiler locust

      - name: Run concurrent users load tests
        run: |
          cd ${{ env.WORKING_DIR }}
          
          # Set environment variables
          export TESTING_PROFILE="${{ needs.preflight-checks.outputs.testing_profile }}"
          export SKIP_MEMORY_PROFILING="${{ github.event.inputs.skip_memory_profiling || 'false' }}"
          export MAX_DURATION_HOURS="${{ github.event.inputs.max_duration_hours || '1' }}"
          
          # Run concurrent users tests
          echo "Running concurrent users load tests with profile: $TESTING_PROFILE"
          pytest tests/performance/test_concurrent_users.py \
            -v --tb=short \
            --maxfail=3 \
            --timeout=3600 \
            --junitxml=concurrent-users-results.xml \
            -m "not long_running or profile_${TESTING_PROFILE}"

      - name: Upload concurrent users test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: concurrent-users-results-${{ needs.preflight-checks.outputs.testing_profile }}
          path: |
            ${{ env.WORKING_DIR }}/concurrent-users-results.xml
            ${{ env.WORKING_DIR }}/test_results/concurrent_users/
          retention-days: 30

  # Phase 2 Sustained Load Testing
  sustained-load-testing:
    runs-on: ubuntu-latest
    needs: [preflight-checks, setup-infrastructure]
    if: |
      needs.preflight-checks.outputs.should_run_tests == 'true' && 
      contains(needs.preflight-checks.outputs.test_scenarios, 'sustained_load')
    timeout-minutes: 480 # 8 hours max for sustained testing
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: ./.github/actions/setup-python
        with:
          service_name: ${{ env.SERVICE_NAME }}

      - name: Install additional dependencies
        run: |
          cd ${{ env.WORKING_DIR }}
          pip install psutil memory_profiler tracemalloc

      - name: Run sustained load tests
        run: |
          cd ${{ env.WORKING_DIR }}
          
          export TESTING_PROFILE="${{ needs.preflight-checks.outputs.testing_profile }}"
          export SKIP_MEMORY_PROFILING="${{ github.event.inputs.skip_memory_profiling || 'false' }}"
          export MAX_DURATION_HOURS="${{ github.event.inputs.max_duration_hours || '4' }}"
          
          echo "Running sustained load tests with profile: $TESTING_PROFILE"
          pytest tests/performance/test_sustained_load.py \
            -v --tb=short \
            --maxfail=1 \
            --timeout=14400 \
            --junitxml=sustained-load-results.xml \
            -m "not endurance or profile_${TESTING_PROFILE}"

      - name: Analyze memory leak detection results
        if: always()
        run: |
          cd ${{ env.WORKING_DIR }}
          
          # Generate memory leak analysis report
          python3 -c "
          import json
          import os
          from pathlib import Path
          
          results_dir = Path('test_results/sustained_load')
          if results_dir.exists():
              for result_file in results_dir.glob('*_memory_analysis.json'):
                  with open(result_file) as f:
                      data = json.load(f)
                  
                  print(f'=== Memory Analysis: {result_file.name} ===')
                  print(f'Memory Growth Rate: {data.get(\"growth_rate_mb_per_hour\", \"N/A\")} MB/hour')
                  print(f'Total Memory Growth: {data.get(\"total_growth_mb\", \"N/A\")} MB')
                  print(f'Potential Memory Leak: {data.get(\"potential_leak_detected\", \"Unknown\")}')
                  print(f'GC Efficiency: {data.get(\"gc_efficiency\", \"N/A\")}')
                  print()
          "

      - name: Upload sustained load test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: sustained-load-results-${{ needs.preflight-checks.outputs.testing_profile }}
          path: |
            ${{ env.WORKING_DIR }}/sustained-load-results.xml
            ${{ env.WORKING_DIR }}/test_results/sustained_load/
          retention-days: 30

  # Phase 2 Realistic Workloads Testing
  realistic-workloads-testing:
    runs-on: ubuntu-latest
    needs: [preflight-checks, setup-infrastructure]
    if: |
      needs.preflight-checks.outputs.should_run_tests == 'true' && 
      contains(needs.preflight-checks.outputs.test_scenarios, 'realistic_workloads')
    timeout-minutes: 360  # 6 hours max
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: ./.github/actions/setup-python
        with:
          service_name: ${{ env.SERVICE_NAME }}

      - name: Install additional dependencies
        run: |
          cd ${{ env.WORKING_DIR }}
          pip install psutil memory_profiler faker

      - name: Run realistic workloads tests
        run: |
          cd ${{ env.WORKING_DIR }}
          
          export TESTING_PROFILE="${{ needs.preflight-checks.outputs.testing_profile }}"
          export SKIP_MEMORY_PROFILING="${{ github.event.inputs.skip_memory_profiling || 'false' }}"
          
          echo "Running realistic workloads tests with profile: $TESTING_PROFILE"
          pytest tests/performance/test_realistic_workloads.py \
            -v --tb=short \
            --maxfail=2 \
            --timeout=7200 \
            --junitxml=realistic-workloads-results.xml \
            -m "not enterprise or profile_${TESTING_PROFILE}"

      - name: Generate workload performance summary
        if: always()
        run: |
          cd ${{ env.WORKING_DIR }}
          
          # Generate performance summary
          python3 -c "
          import json
          import glob
          from pathlib import Path
          
          results_dir = Path('test_results/realistic_workloads')
          if results_dir.exists():
              print('=== Realistic Workloads Performance Summary ===')
              
              for result_file in results_dir.glob('*_performance.json'):
                  with open(result_file) as f:
                      data = json.load(f)
                  
                  workload = result_file.stem.replace('_performance', '')
                  print(f'\\n{workload.upper()} Workload:')
                  print(f'  Average Response Time: {data.get(\"avg_response_time\", \"N/A\")}ms')
                  print(f'  95th Percentile: {data.get(\"p95_response_time\", \"N/A\")}ms')
                  print(f'  Throughput: {data.get(\"requests_per_second\", \"N/A\")} req/s')
                  print(f'  Error Rate: {data.get(\"error_rate\", \"N/A\")}%')
                  print(f'  SLA Compliance: {data.get(\"sla_compliance\", \"N/A\")}%')
          "

      - name: Upload realistic workloads test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: realistic-workloads-results-${{ needs.preflight-checks.outputs.testing_profile }}
          path: |
            ${{ env.WORKING_DIR }}/realistic-workloads-results.xml
            ${{ env.WORKING_DIR }}/test_results/realistic_workloads/
          retention-days: 30

  # Phase 2 Scalability Limits Testing
  scalability-limits-testing:
    runs-on: ubuntu-latest
    needs: [preflight-checks, setup-infrastructure]
    if: |
      needs.preflight-checks.outputs.should_run_tests == 'true' && 
      contains(needs.preflight-checks.outputs.test_scenarios, 'scalability_limits')
    timeout-minutes: 300  # 5 hours max
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: ./.github/actions/setup-python
        with:
          service_name: ${{ env.SERVICE_NAME }}

      - name: Install additional dependencies
        run: |
          cd ${{ env.WORKING_DIR }}
          pip install psutil memory_profiler numpy scipy

      - name: Run scalability limits tests
        run: |
          cd ${{ env.WORKING_DIR }}
          
          export TESTING_PROFILE="${{ needs.preflight-checks.outputs.testing_profile }}"
          export SKIP_MEMORY_PROFILING="${{ github.event.inputs.skip_memory_profiling || 'false' }}"
          
          echo "Running scalability limits tests with profile: $TESTING_PROFILE"
          pytest tests/performance/test_scalability_limits.py \
            -v --tb=short \
            --maxfail=1 \
            --timeout=10800 \
            --junitxml=scalability-limits-results.xml \
            -m "profile_${TESTING_PROFILE}"

      - name: Generate scalability analysis report
        if: always()
        run: |
          cd ${{ env.WORKING_DIR }}
          
          # Generate scalability analysis
          python3 -c "
          import json
          from pathlib import Path
          
          results_dir = Path('test_results/scalability_limits')
          if results_dir.exists():
              print('=== Scalability Analysis Report ===')
              
              analysis_file = results_dir / 'scalability_analysis.json'
              if analysis_file.exists():
                  with open(analysis_file) as f:
                      data = json.load(f)
                  
                  print(f'\\nBreaking Point Analysis:')
                  print(f'  Maximum Concurrent Users: {data.get(\"max_concurrent_users\", \"N/A\")}')
                  print(f'  Maximum Throughput: {data.get(\"max_throughput_rps\", \"N/A\")} req/s')
                  print(f'  Performance Cliff Point: {data.get(\"performance_cliff_users\", \"N/A\")} users')
                  print(f'  Efficiency Score: {data.get(\"efficiency_score\", \"N/A\")}/100')
                  
                  bottlenecks = data.get('bottlenecks', [])
                  if bottlenecks:
                      print(f'\\nIdentified Bottlenecks:')
                      for bottleneck in bottlenecks:
                          print(f'  - {bottleneck}')
                  
                  recommendations = data.get('scaling_recommendations', [])
                  if recommendations:
                      print(f'\\nScaling Recommendations:')
                      for rec in recommendations:
                          print(f'  - {rec}')
          "

      - name: Upload scalability limits test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: scalability-limits-results-${{ needs.preflight-checks.outputs.testing_profile }}
          path: |
            ${{ env.WORKING_DIR }}/scalability-limits-results.xml
            ${{ env.WORKING_DIR }}/test_results/scalability_limits/
          retention-days: 30

  # Generate comprehensive test report
  generate-report:
    runs-on: ubuntu-latest
    needs: [
      preflight-checks, 
      concurrent-users-testing, 
      sustained-load-testing, 
      realistic-workloads-testing, 
      scalability-limits-testing
    ]
    if: always() && needs.preflight-checks.outputs.should_run_tests == 'true'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all test results
        uses: actions/download-artifact@v4
        with:
          path: test-results

      - name: Generate comprehensive report
        run: |
          # Create comprehensive HTML report
          cat > load-test-report.html << 'EOF'
          <!DOCTYPE html>
          <html>
          <head>
              <title>Pattern Mining Phase 2 Load Testing Report</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 40px; }
                  .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }
                  .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                  .success { background: #d4edda; border-color: #c3e6cb; }
                  .warning { background: #fff3cd; border-color: #ffeaa7; }
                  .error { background: #f8d7da; border-color: #f5c6cb; }
                  table { width: 100%; border-collapse: collapse; }
                  th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
                  th { background-color: #f2f2f2; }
              </style>
          </head>
          <body>
              <div class="header">
                  <h1>Pattern Mining Phase 2 Load Testing Report</h1>
                  <p>Generated: $(date)</p>
                  <p>Testing Profile: ${{ needs.preflight-checks.outputs.testing_profile }}</p>
                  <p>Test Scenarios: ${{ needs.preflight-checks.outputs.test_scenarios }}</p>
              </div>
          EOF
          
          # Add test results summary
          echo '<div class="section">' >> load-test-report.html
          echo '<h2>Test Execution Summary</h2>' >> load-test-report.html
          echo '<table>' >> load-test-report.html
          echo '<tr><th>Test Suite</th><th>Status</th><th>Duration</th><th>Key Metrics</th></tr>' >> load-test-report.html
          
          # Check each test result
          if [ "${{ needs.concurrent-users-testing.result }}" = "success" ]; then
              echo '<tr><td>Concurrent Users</td><td class="success">✅ Passed</td><td>-</td><td>See detailed results</td></tr>' >> load-test-report.html
          else
              echo '<tr><td>Concurrent Users</td><td class="error">❌ Failed</td><td>-</td><td>Check logs for details</td></tr>' >> load-test-report.html
          fi
          
          if [ "${{ needs.sustained-load-testing.result }}" = "success" ]; then
              echo '<tr><td>Sustained Load</td><td class="success">✅ Passed</td><td>-</td><td>No memory leaks detected</td></tr>' >> load-test-report.html
          else
              echo '<tr><td>Sustained Load</td><td class="error">❌ Failed</td><td>-</td><td>Check memory analysis</td></tr>' >> load-test-report.html
          fi
          
          if [ "${{ needs.realistic-workloads-testing.result }}" = "success" ]; then
              echo '<tr><td>Realistic Workloads</td><td class="success">✅ Passed</td><td>-</td><td>SLA compliance verified</td></tr>' >> load-test-report.html
          else
              echo '<tr><td>Realistic Workloads</td><td class="error">❌ Failed</td><td>-</td><td>SLA violations detected</td></tr>' >> load-test-report.html
          fi
          
          if [ "${{ needs.scalability-limits-testing.result }}" = "success" ]; then
              echo '<tr><td>Scalability Limits</td><td class="success">✅ Passed</td><td>-</td><td>Breaking points identified</td></tr>' >> load-test-report.html
          else
              echo '<tr><td>Scalability Limits</td><td class="error">❌ Failed</td><td>-</td><td>Analysis incomplete</td></tr>' >> load-test-report.html
          fi
          
          echo '</table>' >> load-test-report.html
          echo '</div>' >> load-test-report.html
          
          # Add recommendations section
          echo '<div class="section">' >> load-test-report.html
          echo '<h2>Key Findings & Recommendations</h2>' >> load-test-report.html
          echo '<ul>' >> load-test-report.html
          echo '<li>Review detailed test artifacts for specific performance metrics</li>' >> load-test-report.html
          echo '<li>Monitor memory usage patterns for production deployment</li>' >> load-test-report.html
          echo '<li>Consider scaling recommendations from scalability analysis</li>' >> load-test-report.html
          echo '<li>Validate SLA compliance against business requirements</li>' >> load-test-report.html
          echo '</ul>' >> load-test-report.html
          echo '</div>' >> load-test-report.html
          
          echo '</body></html>' >> load-test-report.html

      - name: Upload comprehensive report
        uses: actions/upload-artifact@v4
        with:
          name: phase2-load-testing-report-${{ needs.preflight-checks.outputs.testing_profile }}
          path: |
            load-test-report.html
            test-results/
          retention-days: 90

      - name: Comment on PR (if applicable)
        uses: actions/github-script@v7
        if: github.event_name == 'pull_request'
        with:
          script: |
            const profile = '${{ needs.preflight-checks.outputs.testing_profile }}';
            const scenarios = '${{ needs.preflight-checks.outputs.test_scenarios }}';
            
            let status = '✅ All tests passed';
            if ('${{ needs.concurrent-users-testing.result }}' !== 'success' ||
                '${{ needs.sustained-load-testing.result }}' !== 'success' ||
                '${{ needs.realistic-workloads-testing.result }}' !== 'success' ||
                '${{ needs.scalability-limits-testing.result }}' !== 'success') {
              status = '⚠️ Some tests failed or were skipped';
            }
            
            const report = `## 🚀 Phase 2 Load Testing Results
            
            **Testing Profile:** \`${profile}\`  
            **Test Scenarios:** \`${scenarios}\`  
            **Overall Status:** ${status}
            
            ### Test Suite Results
            - **Concurrent Users:** ${{ needs.concurrent-users-testing.result == 'success' && '✅ Passed' || '❌ Failed' }}
            - **Sustained Load:** ${{ needs.sustained-load-testing.result == 'success' && '✅ Passed' || '❌ Failed' }}
            - **Realistic Workloads:** ${{ needs.realistic-workloads-testing.result == 'success' && '✅ Passed' || '❌ Failed' }}
            - **Scalability Limits:** ${{ needs.scalability-limits-testing.result == 'success' && '✅ Passed' || '❌ Failed' }}
            
            📊 **Detailed Results:** Check the comprehensive report in test artifacts
            
            💡 **Note:** Phase 2 load testing validates production readiness with advanced scenarios including memory leak detection, realistic user behavior simulation, and scalability analysis.
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: report
            });

  # Cleanup test infrastructure
  cleanup:
    runs-on: ubuntu-latest
    needs: [
      setup-infrastructure,
      concurrent-users-testing, 
      sustained-load-testing, 
      realistic-workloads-testing, 
      scalability-limits-testing
    ]
    if: always() && needs.setup-infrastructure.outputs.infrastructure_ready == 'true'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Cleanup test infrastructure
        run: |
          cd ${{ env.WORKING_DIR }}
          
          # Stop all containers and clean up
          docker-compose down -v --remove-orphans || true
          
          # Clean up any remaining containers
          docker container prune -f || true
          docker volume prune -f || true
          
          echo "Infrastructure cleanup completed!"