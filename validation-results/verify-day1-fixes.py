#!/usr/bin/env python3
"""
Verify Day 1 fixes are working correctly.
Tests port configurations and JWT authentication.
"""

import requests
import json
import jwt
import time
from datetime import datetime, timedelta, timezone

# Service endpoints
SERVICES = {
    "analysis-engine": "http://localhost:8001",
    "query-intelligence": "http://localhost:8002", 
    "pattern-mining": "http://localhost:8003",
    "marketplace": "http://localhost:8004",
    "collaboration": "http://localhost:8005"
}

# JWT configuration matching all services
JWT_SECRET = "your-secret-key-here"  # This should match the actual secret
JWT_ALGORITHM = "HS256"
JWT_ISSUER = "ccl-platform"

def generate_test_jwt(service_name):
    """Generate a test JWT token with proper claims."""
    now = datetime.now(timezone.utc)
    
    payload = {
        "sub": "test-user-123",
        "iss": JWT_ISSUER,
        "aud": f"ccl-{service_name}",
        "exp": now + timedelta(hours=1),
        "iat": now,
        "nbf": now,
        "jti": f"test-{int(time.time())}",
        "scope": "read write",
        "session_id": "test-session",
        "device_id": "test-device",
        "user_id": "test-user-123",
        "email": "<EMAIL>",
        "roles": ["user", "developer"]
    }
    
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

def check_service_health(name, url):
    """Check if a service is running and healthy."""
    print(f"\n🔍 Checking {name}...")
    
    try:
        # Try health endpoint
        response = requests.get(f"{url}/health", timeout=5)
        if response.status_code == 200:
            print(f"  ✅ {name} is running on {url}")
            return True
        else:
            print(f"  ⚠️  {name} returned status {response.status_code}")
            
    except requests.ConnectionError:
        print(f"  ❌ {name} is not running on {url}")
    except Exception as e:
        print(f"  ❌ Error checking {name}: {e}")
    
    return False

def test_jwt_auth(name, url):
    """Test JWT authentication on a service."""
    print(f"\n🔐 Testing JWT auth for {name}...")
    
    # Generate appropriate audience for the service
    audience_map = {
        "analysis-engine": "analysis-engine",
        "query-intelligence": "query-intelligence",
        "pattern-mining": "pattern-mining",
        "marketplace": "marketplace",
        "collaboration": "collaboration"
    }
    
    token = generate_test_jwt(audience_map.get(name, name))
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Try a protected endpoint
        test_endpoints = [
            "/api/v1/status",
            "/api/v1/user",
            "/api/health",
            "/status"
        ]
        
        for endpoint in test_endpoints:
            try:
                response = requests.get(f"{url}{endpoint}", headers=headers, timeout=5)
                if response.status_code in [200, 401, 403, 404]:
                    print(f"  ✅ JWT middleware is active (endpoint: {endpoint}, status: {response.status_code})")
                    return True
            except:
                continue
                
        print(f"  ⚠️  Could not verify JWT middleware")
        
    except Exception as e:
        print(f"  ❌ Error testing JWT: {e}")
    
    return False

def main():
    """Run all verification tests."""
    print("=" * 60)
    print("Day 1 Fixes Verification")
    print("=" * 60)
    
    # Check if services are running
    print("\n📊 Service Status Check:")
    running_services = {}
    for name, url in SERVICES.items():
        running_services[name] = check_service_health(name, url)
    
    # Check for port conflicts
    print("\n🔌 Port Conflict Check:")
    if running_services.get("analysis-engine") and running_services.get("pattern-mining"):
        print("  ✅ No port conflict - services can run together!")
    else:
        print("  ⚠️  Cannot verify - ensure both services are running")
    
    # Test JWT authentication
    print("\n🔒 JWT Authentication Tests:")
    for name, url in SERVICES.items():
        if running_services.get(name):
            test_jwt_auth(name, url)
    
    # Summary
    print("\n" + "=" * 60)
    print("Summary:")
    print("=" * 60)
    
    running_count = sum(1 for v in running_services.values() if v)
    print(f"✅ Services running: {running_count}/{len(SERVICES)}")
    
    if running_services.get("analysis-engine") and running_services.get("pattern-mining"):
        print("✅ Port conflict RESOLVED - services can coexist")
    else:
        print("⚠️  Port conflict resolution needs verification")
    
    print("\n💡 Next Steps:")
    if running_count < len(SERVICES):
        print("1. Start missing services to complete verification")
    print("2. Implement AST transformation layer")
    print("3. Run full integration tests")
    
    # Save results
    results = {
        "timestamp": datetime.now().isoformat(),
        "services_checked": len(SERVICES),
        "services_running": running_count,
        "port_conflict_resolved": running_services.get("analysis-engine", False) and running_services.get("pattern-mining", False),
        "service_status": running_services
    }
    
    with open("day1-verification-results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Results saved to day1-verification-results.json")

if __name__ == "__main__":
    main()