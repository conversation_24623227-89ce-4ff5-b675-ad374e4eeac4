#!/bin/bash
# Quick check if services are running on expected ports

echo "Checking Episteme services..."
echo "=============================="

# Function to check if a port is in use
check_port() {
    local port=$1
    local service=$2
    
    if lsof -i :$port > /dev/null 2>&1; then
        echo "✅ Port $port: $service is running"
    else
        echo "❌ Port $port: $service is NOT running"
    fi
}

# Check each service
check_port 8001 "Analysis Engine"
check_port 8002 "Query Intelligence"
check_port 8003 "Pattern Mining"
check_port 8004 "Marketplace"
check_port 8005 "Collaboration"

echo ""
echo "Port conflict check:"
echo "===================="

# Check specifically for the old conflict
if lsof -i :8001 > /dev/null 2>&1 && lsof -i :8003 > /dev/null 2>&1; then
    echo "✅ RESOLVED: Analysis Engine (8001) and Pattern Mining (8003) can run together!"
else
    echo "⚠️  Cannot verify resolution - start both services to confirm"
fi

echo ""
echo "To start services, use:"
echo "  cd services/[service-name] && make run"
echo "  OR"
echo "  docker-compose up [service-name]"