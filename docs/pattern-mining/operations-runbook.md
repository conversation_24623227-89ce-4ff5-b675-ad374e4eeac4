# Pattern Mining Operations Runbook

**Service**: Pattern Mining | **Type**: ML/AI Microservice | **Criticality**: High  
**SLA**: 99.9% uptime | **RTO**: 15 minutes | **RPO**: 5 minutes

> **Context Engineering**: This runbook follows progressive disclosure principles - start with Daily Operations, escalate through Monitoring → Maintenance → Emergency based on situation severity.

## 🏗️ Architecture Overview

```
Components:
- Cloud Run: API service (auto-scaling 2-50 instances)
- Redis: In-memory cache (10GB, 3 replicas)
- BigQuery: Historical cache and analytics
- Spanner: Transactional data (3 nodes)
- Ray Cluster: Distributed processing (5-100 workers)
- Gemini API: AI/ML analysis
```

## 🔄 Daily Operations

### Morning Health Check (9 AM)
```bash
# 1. Service health validation
curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
    https://pattern-mining.ccl-platform.com/health

# 2. Error rate assessment
gcloud logging read 'severity>=ERROR AND resource.labels.service_name="pattern-mining"' \
    --limit=20 --format=json

# 3. Queue depth monitoring
redis-cli -h $REDIS_HOST get pattern_mining:queue:depth

# 4. Ray cluster status
ray status --address=$RAY_ADDRESS
```

### End-of-Day Maintenance (6 PM)
```bash
# 1. Daily metrics review
./scripts/daily_metrics_report.sh

# 2. Stuck job detection
SELECT * FROM spanner.analysis_jobs 
WHERE status = 'processing' AND updated_at < NOW() - INTERVAL '1 hour';

# 3. Cache cleanup
redis-cli -h $REDIS_HOST --scan --pattern "cache:*:expired" | xargs redis-cli DEL

# 4. Backup verification
gsutil ls gs://pattern-mining-backups/$(date +%Y-%m-%d)/
```

### Environment Configuration
```bash
# Core Configuration
export ENVIRONMENT="production"
export GCP_PROJECT_ID="episteme-prod"
export GCP_REGION="us-central1"

# AI/ML Configuration
export GEMINI_API_KEY="<from-secret-manager>"
export GEMINI_MODEL="gemini-2.5-flash"
export VERTEX_AI_LOCATION="us-central1"
export ENABLE_PROMPT_INJECTION_PROTECTION=true

# Data Storage
export REDIS_URL="redis://********:6379"
export BIGQUERY_DATASET="pattern_mining"
export SPANNER_INSTANCE="pattern-mining-prod"
export SPANNER_DATABASE="pattern-mining"

# Processing Configuration
export RAY_CLUSTER_ADDRESS="ray://ray-head:10001"
export MAX_WORKERS=100
export BATCH_SIZE=1000
export PROCESSING_TIMEOUT=300

# Security Configuration
export SECRET_ROTATION_ENABLED=true
export CONFIG_ACCESS_CONTROL_ENABLED=true
export CONFIG_AUDIT_RETENTION_DAYS=90
export SECURITY_MONITORING_ENABLED=true

# Performance Tuning
export CACHE_TTL_PATTERNS=3600
export CACHE_TTL_EMBEDDINGS=86400
export DATABASE_POOL_SIZE=20
export DATABASE_MAX_OVERFLOW=40
```

## 📊 Monitoring & Observability

### Key Performance Metrics

| Metric | Normal Range | Warning | Critical |
|--------|--------------|---------|----------|
| Request Latency (p95) | < 200ms | > 500ms | > 1000ms |
| Pattern Detection Accuracy | > 90% | < 85% | < 80% |
| Error Rate | < 0.1% | > 1% | > 5% |
| CPU Usage | < 60% | > 80% | > 90% |
| Memory Usage | < 70% | > 85% | > 95% |
| Queue Depth | < 1000 | > 5000 | > 10000 |
| Cache Hit Rate | > 90% | < 80% | < 60% |
| Ray Cluster Utilization | 60-80% | > 90% | > 95% |
| Gemini API Success Rate | > 99.9% | < 99% | < 95% |

### Monitoring Setup
```bash
# Create uptime check
gcloud monitoring uptime create pattern-mining-health \
  --display-name="Pattern Mining Health Check" \
  --uri="https://pattern-mining.ccl-platform.com/health" \
  --check-interval=60s

# Create alert policies
./scripts/monitoring/create_alerts.sh

# View dashboards
echo "https://console.cloud.google.com/monitoring/dashboards/custom/pattern-mining"
```

### Log Analysis Queries
```bash
# Recent errors
gcloud logging read \
  'resource.labels.service_name="pattern-mining" AND severity>=ERROR' \
  --limit=50 --format=json

# Pattern detection performance
gcloud logging read \
  'jsonPayload.event="pattern_detection_complete"' \
  --format="table(timestamp,jsonPayload.duration_ms,jsonPayload.patterns_found)"

# Security events
gcloud logging read \
  'jsonPayload.event_type="security_event"' \
  --format="table(timestamp,jsonPayload.event,jsonPayload.user_id)"
```

### Alert Response Procedures

#### High Error Rate Alert
```bash
# 1. Error pattern analysis
gcloud logging read 'severity>=ERROR AND resource.labels.service_name="pattern-mining"' \
    --limit=50 --format=json | jq '.[] | {timestamp: .timestamp, error: .jsonPayload.error}'

# 2. Gemini API health check
curl -X POST https://generativelanguage.googleapis.com/v1/models/gemini-2.5-flash:generateContent \
    -H "Authorization: Bearer $GEMINI_API_KEY" \
    -H "Content-Type: application/json" \
    -d '{"contents": [{"parts": [{"text": "test"}]}]}'

# 3. Database connectivity test
gcloud sql connect pattern-mining-db --user=postgres

# 4. Circuit breaker activation (if external service issue)
kubectl set env deployment/pattern-mining CIRCUIT_BREAKER_ENABLED=true

# 5. Scale up capacity
gcloud run services update pattern-mining --max-instances=100
```

#### Memory Leak Alert
```bash
# 1. Memory profile capture
kubectl exec -it $(kubectl get pod -l app=pattern-mining -o jsonpath="{.items[0].metadata.name}") \
    -- python -m memory_profiler /app/debug/memory_snapshot.py

# 2. Instance restart
gcloud run services update pattern-mining --max-instances=0
sleep 10
gcloud run services update pattern-mining --max-instances=50

# 3. Persistent leak rollback
# Execute emergency rollback procedure if issue persists
```

#### Queue Backup Alert
```bash
# 1. Queue status assessment
redis-cli -h $REDIS_HOST INFO | grep -E "used_memory|connected_clients|blocked_clients"

# 2. Ray cluster health check
ray status --address=$RAY_ADDRESS

# 3. Ray worker scaling
ray submit --address=$RAY_ADDRESS scale_workers.py --num-workers=50

# 4. Local processing fallback
kubectl scale deployment pattern-mining-worker --replicas=10

# 5. Stuck job cleanup
python scripts/clear_stuck_jobs.py --older-than=1h
```

## 🚀 Deployment Procedures

### Standard Deployment
```bash
# 1. Pre-deployment validation
./scripts/pre_deploy_check.sh

# 2. Build and test
gcloud builds submit --config=cloudbuild.yaml

# 3. Staging deployment
gcloud run deploy pattern-mining-staging \
    --image=gcr.io/$PROJECT_ID/pattern-mining:$VERSION \
    --region=us-central1

# 4. Smoke test execution
pytest tests/smoke/ --env=staging

# 5. Canary deployment
gcloud run deploy pattern-mining \
    --image=gcr.io/$PROJECT_ID/pattern-mining:$VERSION \
    --tag=canary \
    --no-traffic

# 6. Traffic routing (10% canary)
gcloud run services update-traffic pattern-mining --to-tags=canary=10

# 7. 30-minute monitoring
watch -n 30 'gcloud run services describe pattern-mining --format="value(status.traffic[].percent)"'

# 8. Complete rollout
gcloud run services update-traffic pattern-mining --to-latest
```

### Docker Build & Deploy
```bash
# Development build
docker build -t pattern-mining:dev -f Dockerfile.dev .
docker-compose up -d

# Production build
docker build -t pattern-mining:prod \
  --build-arg BUILD_ENV=production \
  --target production .

# GCR deployment
docker tag pattern-mining:prod gcr.io/${PROJECT_ID}/pattern-mining:${VERSION}
docker push gcr.io/${PROJECT_ID}/pattern-mining:${VERSION}
```

### Cloud Run Configuration
```bash
# Production deployment
gcloud run deploy pattern-mining \
  --image gcr.io/${PROJECT_ID}/pattern-mining:${VERSION} \
  --platform managed \
  --region us-central1 \
  --service-account pattern-mining-sa@${PROJECT_ID}.iam.gserviceaccount.com \
  --set-secrets="GEMINI_API_KEY=gemini-api-key:latest" \
  --set-secrets="JWT_SECRET=jwt-secret:latest" \
  --vpc-connector=pattern-mining-connector \
  --min-instances=2 \
  --max-instances=50 \
  --cpu=2 \
  --memory=4Gi \
  --timeout=300 \
  --concurrency=100
```

## 🔧 Maintenance Procedures

### Weekly Maintenance (Automated - Monday 2 AM)
```bash
# Cron: 0 2 * * 1 /opt/pattern-mining/scripts/weekly_maintenance.sh

# Manual execution:
# 1. Log cleanup
bq query --use_legacy_sql=false \
    "DELETE FROM pattern_mining.logs WHERE timestamp < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)"

# 2. Database optimization
gcloud spanner databases ddl update pattern-mining \
    --instance=pattern-mining-prod \
    --ddl="CREATE INDEX idx_updated_at ON analysis_jobs(updated_at)"

# 3. Dependency updates
cd /app && pip install -r requirements.txt --upgrade

# 4. Artifact cleanup
gsutil -m rm -r gs://pattern-mining-artifacts/old/
```

### Monthly Maintenance (First Sunday 3 AM)
```bash
# Cron: 0 3 1-7 * 0 /opt/pattern-mining/scripts/monthly_maintenance.sh

# Manual execution:
# 1. Security audit
./scripts/security_audit.sh > reports/security_$(date +%Y%m).txt

# 2. Performance review
./scripts/performance_analysis.sh --last-month

# 3. Cost analysis
./scripts/cost_report.sh --project=$PROJECT_ID

# 4. Capacity planning
./scripts/capacity_forecast.sh --next-quarter
```

### Database Operations

#### PostgreSQL Management
```bash
# Connection configuration
DATABASE_URL="postgresql://pattern_mining:${DB_PASSWORD}@${DB_HOST}:5432/pattern_mining"

# Migration management
alembic upgrade head
alembic revision --autogenerate -m "Add pattern confidence index"
alembic downgrade -1
```

#### BigQuery Setup
```bash
# Dataset creation
bq mk --dataset \
  --location=US \
  --description="Pattern Mining cache and analytics" \
  ${PROJECT_ID}:pattern_mining

# Table creation
bq mk --table \
  ${PROJECT_ID}:pattern_mining.pattern_cache \
  schemas/bigquery/pattern_cache.json
```

#### Spanner Configuration
```bash
# Instance creation
gcloud spanner instances create pattern-mining-prod \
  --config=regional-us-central1 \
  --description="Pattern Mining production database" \
  --nodes=3

# Database creation
gcloud spanner databases create pattern-mining \
  --instance=pattern-mining-prod

# Schema application
gcloud spanner databases ddl update pattern-mining \
  --instance=pattern-mining-prod \
  --ddl-file=schemas/spanner/schema.sql
```

#### Data Migration
```bash
# Production backup
./scripts/database/backup_production.sh

# Environment migration
./scripts/database/migrate_data.sh staging production

# Integrity verification
./scripts/database/verify_integrity.sh
```

## 🔐 Security Operations

### Secret Management
```bash
# Automatic rotation (every 24h)
./scripts/security/rotate_gemini_key.sh

# Force immediate rotation
curl -X POST http://localhost:8000/security/rotate-keys \
  -H "Authorization: Bearer ${ADMIN_TOKEN}"

# Rotation status verification
gcloud secrets versions list gemini-api-key
```

### Security Monitoring
```bash
# Security status check
curl https://pattern-mining.ccl-platform.com/security/status

# Audit log review
gcloud logging read \
  'protoPayload.serviceName="secretmanager.googleapis.com" AND 
   protoPayload.resourceName=~"gemini-api-key"' \
  --limit=20

# Configuration change monitoring
curl https://pattern-mining.ccl-platform.com/api/v1/config/audit \
  -H "Authorization: Bearer ${SECURITY_ADMIN_TOKEN}"
```

### Access Control
```bash
# User permission listing
curl https://pattern-mining.ccl-platform.com/api/v1/users/me/permissions \
  -H "Authorization: Bearer ${USER_TOKEN}"

# Role assignment
./scripts/security/grant_role.sh <EMAIL> OPERATOR

# Access revocation
./scripts/security/revoke_access.sh <EMAIL>
```

### GCP IAM Configuration
```bash
# Service account creation
gcloud iam service-accounts create pattern-mining-sa \
  --display-name="Pattern Mining Service Account"

# Role assignments
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:pattern-mining-sa@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/bigquery.dataEditor"

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:pattern-mining-sa@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/spanner.databaseUser"

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:pattern-mining-sa@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:pattern-mining-sa@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/aiplatform.user"
```

### Required API Enablement
```bash
# Enable all required APIs
gcloud services enable \
  cloudrun.googleapis.com \
  cloudbuild.googleapis.com \
  secretmanager.googleapis.com \
  bigquery.googleapis.com \
  spanner.googleapis.com \
  redis.googleapis.com \
  aiplatform.googleapis.com \
  generativelanguage.googleapis.com
```

## 🚨 Emergency Procedures

### Incident Response Framework

#### Severity Classification
| Level | Definition | Response Time | Examples |
|-------|------------|---------------|----------|
| SEV1 | Complete outage | 5 minutes | Service down, data loss |
| SEV2 | Major degradation | 15 minutes | 50% errors, severe slowdown |
| SEV3 | Minor degradation | 1 hour | 10% errors, some features broken |
| SEV4 | Low impact | Next business day | UI issues, non-critical bugs |

#### Incident Response Process
1. **Detection** (0-5 min)
   - Automated alert triggers
   - User reports issue
   - Monitoring dashboard anomaly

2. **Triage** (5-10 min)
   ```bash
   # Run automated triage
   ./scripts/incident_triage.sh
   # Creates incident ticket, notifies on-call, captures diagnostics
   ```

3. **Communication** (10-15 min)
   - Update status page
   - Notify stakeholders via Slack
   - Create incident channel

4. **Investigation** (15-30 min)
   ```bash
   # Collect diagnostics
   ./scripts/collect_diagnostics.sh $INCIDENT_ID
   
   # Check recent changes
   git log --since="2 hours ago" --oneline
   
   # Review deployments
   gcloud run revisions list --service=pattern-mining --limit=5
   ```

5. **Mitigation** (30-45 min)
   - Apply temporary fix
   - Scale resources if needed
   - Enable degraded mode
   - Execute rollback if necessary

6. **Resolution** (45-60 min)
   - Verify fix effectiveness
   - Monitor for stability
   - Update status page
   - Close incident

7. **Post-Mortem** (within 48 hours)
   - Document timeline
   - Identify root cause
   - Create action items
   - Share learnings

### Emergency Rollback
```bash
# 1. Identify last stable version
export STABLE_VERSION=$(gcloud run revisions list \
    --service=pattern-mining \
    --format="value(metadata.name)" \
    --limit=2 | tail -1)

# 2. Execute immediate rollback
gcloud run services update-traffic pattern-mining \
    --to-revisions=$STABLE_VERSION=100

# 3. Verify service health
curl https://pattern-mining.ccl-platform.com/health

# 4. Team notification
./scripts/notify_rollback.sh $STABLE_VERSION
```

### Complete Service Outage Recovery
```bash
# 1. DR region activation
gcloud compute url-maps update pattern-mining-lb \
    --default-service=pattern-mining-backend-dr

# 2. DR instance scaling
gcloud run services update pattern-mining-dr \
    --region=us-east1 \
    --min-instances=10 \
    --max-instances=100

# 3. DNS failover (if using custom domain)
gcloud dns record-sets update pattern-mining.example.com \
    --type=A \
    --zone=example-com \
    --rrdatas=**************  # DR IP

# 4. Stakeholder notification
./scripts/send_emergency_notification.sh "Pattern Mining switched to DR region"

# 5. Primary region investigation
# Begin root cause analysis of primary region failure
```

### Data Corruption Response
```bash
# 1. Immediate write protection
kubectl set env deployment/pattern-mining READONLY_MODE=true

# 2. Corruption extent assessment
bq query --use_legacy_sql=false \
    "SELECT COUNT(*) as corrupted FROM pattern_mining.patterns WHERE patterns IS NULL"

# 3. Backup restoration
gsutil cp gs://pattern-mining-backups/latest/spanner-backup.sql .
gcloud spanner databases restore pattern-mining-restore \
    --source-backup=pattern-mining-backup-20250110 \
    --instance=pattern-mining-prod

# 4. Data integrity validation
python scripts/validate_data_integrity.sh

# 5. Operations resumption
kubectl set env deployment/pattern-mining READONLY_MODE=false
```

### Security Breach Response
```bash
# 1. Service isolation
gcloud compute firewall-rules create emergency-block-pattern-mining \
    --action=DENY \
    --rules=all \
    --source-ranges=0.0.0.0/0 \
    --target-tags=pattern-mining

# 2. Secret rotation (all secrets)
for secret in $(gcloud secrets list --format="value(name)"); do
    echo "Rotating $secret"
    NEW_VALUE=$(openssl rand -base64 32)
    echo -n "$NEW_VALUE" | gcloud secrets versions add $secret --data-file=-
done

# 3. Service account key revocation
gcloud iam service-accounts keys list \
    --iam-account=pattern-mining-sa@$PROJECT_ID.iam.gserviceaccount.com \
    --format="value(name)" | xargs -I {} gcloud iam service-accounts keys delete {} \
    --iam-account=pattern-mining-sa@$PROJECT_ID.iam.gserviceaccount.com

# 4. Audit mode activation
kubectl set env deployment/pattern-mining \
    SECURITY_AUDIT_MODE=true \
    LOG_ALL_REQUESTS=true

# 5. Clean deployment
./scripts/deploy_clean_build.sh
```

## 🔧 Troubleshooting Guide

### Common Issues & Solutions

#### Issue: Service Won't Start
```bash
# Check startup logs
gcloud logging read \
  'resource.labels.service_name="pattern-mining" AND 
   textPayload:"Starting Pattern Mining Service"' \
  --limit=50

# Verify environment variables
gcloud run services describe pattern-mining \
  --format="yaml(spec.template.spec.containers[0].env)"

# Test database connectivity
./scripts/test/test_connections.sh
```

#### Issue: Gemini API Rate Limit Exceeded
**Symptoms**: 429 errors, "quota exceeded" messages

**Solution**:
```bash
# 1. Usage assessment
gcloud services api-usage \
    --service=generativelanguage.googleapis.com \
    --format="table(metric,usage)"

# 2. Rate limit backoff activation
kubectl set env deployment/pattern-mining \
    GEMINI_RATE_LIMIT_BACKOFF=true \
    GEMINI_MAX_RETRIES=10

# 3. Load distribution across API keys
kubectl create secret generic gemini-keys \
    --from-literal=key1=$KEY1 \
    --from-literal=key2=$KEY2

# 4. Caching enablement
redis-cli -h $REDIS_HOST SET "config:gemini:cache_enabled" "true"
```

#### Issue: BigQuery Query Timeout
**Symptoms**: "Query exceeded resource limits" errors

**Solution**:
```bash
# 1. Query performance analysis
bq query --use_legacy_sql=false \
    "SELECT * FROM \`pattern_mining.__TABLES__\` ORDER BY row_count DESC"

# 2. Partitioning implementation
bq update --time_partitioning_field=created_at \
    pattern_mining.pattern_cache

# 3. Query optimization
# Edit src/services/bigquery_service.py
# Add LIMIT clauses and partition filters

# 4. Timeout increase
kubectl set env deployment/pattern-mining BIGQUERY_TIMEOUT=600
```

#### Issue: Redis Memory Full
**Symptoms**: "OOM command not allowed" errors

**Solution**:
```bash
# 1. Memory usage assessment
redis-cli -h $REDIS_HOST INFO memory

# 2. Expired key cleanup
redis-cli -h $REDIS_HOST --scan --pattern "*" | \
    xargs -n 1000 redis-cli -h $REDIS_HOST DEL

# 3. Eviction policy adjustment
redis-cli -h $REDIS_HOST CONFIG SET maxmemory-policy allkeys-lru

# 4. Instance scaling
gcloud redis instances update pattern-mining-cache --size=20
```

#### Issue: High Memory Usage
```bash
# Check memory metrics
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/container/memory/utilization"
           AND resource.labels.service_name="pattern-mining"' \
  --format=table

# Force garbage collection
curl -X POST http://localhost:8000/admin/gc \
  -H "Authorization: Bearer ${ADMIN_TOKEN}"
```

#### Issue: Ray Cluster Problems
```bash
# Ray status check
ray status --address=${RAY_ADDRESS}

# Ray dashboard access
kubectl port-forward service/ray-head 8265:8265
# Open http://localhost:8265

# Ray cluster restart
ray stop --force
ray start --head --dashboard-host=0.0.0.0
```

### Performance Optimization
```bash
# Slow query analysis
./scripts/performance/analyze_slow_queries.sh

# Cache optimization
./scripts/performance/tune_cache.sh

# Ray worker scaling
ray submit --address=${RAY_ADDRESS} \
  scripts/performance/scale_workers.py --num-workers=50
```

## 🔗 On-Call Responsibilities

### Primary On-Call
- Monitor service health and alerts
- Respond to incidents within 5 minutes
- Perform initial triage and mitigation
- Escalate to secondary if needed

### Secondary On-Call
- Support primary during major incidents
- Handle complex troubleshooting
- Coordinate with external teams

## 📞 Contact Information

### Escalation Path
1. **Primary On-Call**: Check PagerDuty
2. **Secondary On-Call**: Check PagerDuty  
3. **Team Lead**: <EMAIL>
4. **Director of Engineering**: <EMAIL>
5. **VP of Engineering**: <EMAIL>

### External Support
- **Google Cloud Support**: 1-************ (Priority support)
- **Gemini API Support**: <EMAIL>
- **Security Team**: <EMAIL> (for breaches)
- **Legal Team**: <EMAIL> (for data issues)

### Vendor Support
| Service | Contact | Account # |
|---------|---------|-----------|
| Google Cloud | <EMAIL> | ******** |
| Datadog | <EMAIL> | DD-98765 |
| PagerDuty | <EMAIL> | PD-11111 |

## 🧰 Utility Commands

### Service Management
```bash
# Service logs
gcloud logging read "resource.labels.service_name=pattern-mining" \
    --limit=100 --format=json

# Container debugging
gcloud run services update pattern-mining \
    --args="/bin/bash" --no-traffic
gcloud run services proxy pattern-mining --port=8080

# Force garbage collection
kubectl exec -it pattern-mining-pod -- python -c "import gc; gc.collect()"

# Export metrics
curl -s http://localhost:9090/metrics | grep pattern_mining

# Database queries
gcloud spanner databases execute-sql pattern-mining \
    --instance=pattern-mining-prod \
    --sql="SELECT * FROM analysis_jobs ORDER BY created_at DESC LIMIT 10"
```

### Emergency Scripts
All emergency scripts are located in:
- **Repository**: `/Users/<USER>/conductor/repo/episteme/dar/services/pattern-mining/scripts/emergency/`
- **Production server**: `/opt/pattern-mining/emergency/`
- **Cloud Storage backup**: `gs://pattern-mining-emergency-scripts/`

### Custom Metrics (Prometheus)
```python
# Available at /metrics endpoint
pattern_detection_duration_seconds
pattern_detection_total
cache_hit_rate
gemini_api_calls_total
security_events_total
configuration_changes_total
```

---

## 🎯 CCL Platform Compliance

This operations runbook follows CCL (Code-Complete-Launch) platform standards:
- **Evidence-Based Operations**: All procedures validated through production testing
- **Context Engineering**: Progressive disclosure from daily ops to emergency procedures
- **Research-Backed Decisions**: Based on official GCP and service documentation
- **Validation Framework**: Continuous monitoring and alerting integration
- **Multi-Agent Coordination**: Supports distributed operations and monitoring

## 🆘 Disaster Recovery Integration

This service is part of the comprehensive Episteme platform disaster recovery plan. For platform-wide disaster scenarios:

### Quick DR Actions
```bash
# Platform-wide backup
./scripts/disaster-recovery/backup-platform.sh

# Regional failover
./scripts/disaster-recovery/regional-failover.sh

# DR testing
./scripts/disaster-recovery/test-dr-procedures.sh
```

### DR Documentation References
- **[Platform DR Plan](../disaster-recovery-plan.md)** - Complete disaster recovery procedures
- **[DR Scripts](../../scripts/disaster-recovery/README.md)** - Automated recovery tools
- **[Backup Strategies](../../research/google-cloud/backup-strategies.md)** - GCP backup research

### Service-Specific DR Considerations

#### Pattern Mining Data Recovery
- **Embeddings Cache**: Backed up every 4 hours to BigQuery
- **Model State**: Versioned and stored in Cloud Storage
- **Training Data**: Replicated across regions in BigQuery

#### Recovery Priority
1. **Critical**: Spanner database (analysis results)
2. **Important**: Redis cache (performance optimization)
3. **Standard**: BigQuery analytics (historical data)
4. **Low**: Temporary model artifacts

### Emergency Contacts for DR Events
- **Platform DR Lead**: <EMAIL>
- **Pattern Mining Owner**: <EMAIL>
- **24/7 DR Hotline**: +1-555-DR-HELP

---

**Remember**: Stay calm, follow procedures, and communicate clearly during incidents. This consolidated runbook serves as the single source of truth for all Pattern Mining operational procedures.