"""
Pattern Mining Service - FastAPI Application

Main FastAPI application with contract-compliant API endpoints.

Wave 2.5: CCL Contract Compliance Implementation - Phase 3
"""

import logging
import os
from contextlib import asynccontextmanager
from datetime import datetime

from fastapi import Fast<PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from .v1 import (
    patterns_router,
    health_router,
)
from .v1.middleware import (
    RequestIDMiddleware,
    ErrorHandlerMiddleware,
    PerformanceTrackingMiddleware,
    ContractValidationMiddleware,
)
from .middleware.monitoring import PrometheusMiddleware
from .v1.dependencies import init_dependencies, cleanup_dependencies
from ..contracts.validators import create_contract_compliant_error
from ..config.settings import get_settings

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    logger.info("Starting Pattern Mining Service v1.0.0")
    
    try:
        # Initialize dependencies
        init_dependencies()
        logger.info("Dependencies initialized successfully")
        
        # Optionally enable security middleware
        if os.getenv("ENABLE_SECURITY", "true").lower() in ["1", "true", "yes"]:
            try:
                from ..security.integration import get_security_integration
                security = await get_security_integration()
                await security.setup_middleware(app)
                logger.info("Security middleware enabled")
            except Exception as sec_err:
                logger.error(f"Failed to enable security middleware: {sec_err}")
                if os.getenv("REQUIRE_SECURITY", "false").lower() in ["1", "true", "yes"]:
                    raise
        
        # Store startup time
        app.state.startup_time = datetime.utcnow()
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start service: {e}")
        raise
        
    finally:
        logger.info("Shutting down Pattern Mining Service")
        cleanup_dependencies()
        logger.info("Service shutdown completed")


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle validation errors with contract-compliant response"""
    request_id = getattr(request.state, "request_id", None)
    
    # Create contract-compliant error
    error = create_contract_compliant_error(
        service="pattern-mining",
        error_type="validation",
        message="Request validation failed",
        retryable=False,
        user_message="Invalid request format. Please check the API documentation.",
        correlation_id=request_id,
        context={
            "validation_errors": exc.errors(),
            "body": str(exc.body) if hasattr(exc, 'body') else None,
        }
    )
    
    return JSONResponse(
        status_code=422,
        content=error.dict()
    )


async def http_exception_handler(request: Request, exc: StarletteHTTPException) -> JSONResponse:
    """Handle HTTP exceptions with contract-compliant response"""
    request_id = getattr(request.state, "request_id", None)
    
    # Determine error type based on status code
    if 400 <= exc.status_code < 500:
        error_type = "validation"
        retryable = False
    elif exc.status_code == 503:
        error_type = "external"
        retryable = True
    else:
        error_type = "internal"
        retryable = True
    
    # Create contract-compliant error
    error = create_contract_compliant_error(
        service="pattern-mining",
        error_type=error_type,
        message=str(exc.detail),
        retryable=retryable,
        user_message=str(exc.detail),
        correlation_id=request_id,
        context={
            "status_code": exc.status_code,
            "path": str(request.url.path),
            "method": request.method,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error.dict()
    )


def create_app() -> FastAPI:
    """Create and configure the FastAPI application"""
    
    # Environment configuration
    environment = os.getenv("ENVIRONMENT", "development")
    enable_docs = environment in ["development", "staging"]
    
    # Get settings for port configuration
    settings = get_settings()
    
    app = FastAPI(
        title="Pattern Mining Service",
        description="CCL Contract-Compliant Pattern Detection Service",
        version="1.0.0",
        lifespan=lifespan,
        docs_url="/docs" if enable_docs else None,
        redoc_url="/redoc" if enable_docs else None,
        openapi_url="/openapi.json" if enable_docs else None,
        servers=[
            {
                "url": f"http://localhost:{settings.port}",
                "description": "Local development server"
            },
            {
                "url": "https://pattern-mining.ccl.dev",
                "description": "Production server"
            }
        ],
    )
    
    # Add exception handlers
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    
    # Add middleware (order matters - applied in reverse)
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=os.getenv("CORS_ORIGINS", "*").split(","),
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
        expose_headers=["X-Request-ID", "X-Response-Time", "X-Service-Version"],
    )
    
    # Compression middleware
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Custom middleware
    app.add_middleware(ContractValidationMiddleware)
    app.add_middleware(PerformanceTrackingMiddleware, service_name="pattern-mining")
    app.add_middleware(ErrorHandlerMiddleware)
    app.add_middleware(RequestIDMiddleware)
    app.add_middleware(PrometheusMiddleware, service_name="pattern-mining")
    
    # Include routers
    app.include_router(health_router, prefix="/api/v1", tags=["health"])
    app.include_router(patterns_router, prefix="/api/v1", tags=["patterns"])
    
    # Root endpoint
    @app.get("/", include_in_schema=False)
    async def root():
        return {
            "service": "pattern-mining",
            "version": "1.0.0",
            "status": "operational",
            "contract_version": "1.0.0",
            "endpoints": [
                "/api/v1/patterns/detect",
                "/api/v1/health",
                "/api/v1/ready",
                "/docs",
                "/redoc",
            ]
        }

    # Kubernetes-friendly health aliases at root
    @app.get("/health", include_in_schema=False)
    async def root_health():
        # Delegate to v1 router path
        return {"status": "healthy", "service": "pattern-mining", "version": "1.0.0"}

    @app.get("/ready", include_in_schema=False)
    async def root_ready():
        # Simple readiness indicator; detailed readiness is under /api/v1/ready
        return {"ready": True, "service": "pattern-mining"}

    # Prometheus metrics endpoint
    @app.get("/metrics", include_in_schema=False)
    async def metrics_endpoint():
        from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
        data = generate_latest()  # default registry
        return Response(content=data, media_type=CONTENT_TYPE_LATEST)
    
    # Info endpoint
    @app.get("/info", tags=["info"])
    async def info():
        return {
            "name": "pattern-mining",
            "version": "1.0.0",
            "description": "CCL Contract-Compliant Pattern Detection Service",
            "contract_version": "1.0.0",
            "supported_pattern_types": [
                "design_pattern",
                "anti_pattern",
                "security_vulnerability",
                "performance_issue",
                "code_smell",
                "architectural_pattern",
                "test_pattern",
                "concurrency_pattern",
            ],
            "supported_languages": [
                "python", "rust", "javascript", "typescript", "java",
                "go", "csharp", "cpp", "c", "kotlin", "swift",
                "ruby", "php", "scala",
            ],
            "api_endpoints": [
                {
                    "path": "/api/v1/patterns/detect",
                    "method": "POST",
                    "description": "Detect patterns in AST data"
                },
                {
                    "path": "/api/v1/health",
                    "method": "GET",
                    "description": "Service health check"
                },
                {
                    "path": "/api/v1/ready",
                    "method": "GET",
                    "description": "Service readiness check"
                },
            ]
        }
    
    return app


# Create the application instance
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    # Get settings
    settings = get_settings()
    
    # Run the application
    uvicorn.run(
        "pattern_mining.api.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )