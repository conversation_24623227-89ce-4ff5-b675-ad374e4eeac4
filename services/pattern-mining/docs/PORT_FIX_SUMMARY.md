# Pattern Mining Service Port Configuration Fix

## Issue
The Pattern Mining service had a hardcoded port (8001) in `main.py` that was preventing it from using the configured port from settings. This was blocking the service from running alongside other services which expect it to be on port 8003.

## Changes Made

### 1. Fixed hardcoded port in main.py
- **File**: `src/pattern_mining/api/main.py`
- **Changes**:
  - Added import for `get_settings` from `..config.settings`
  - Modified `uvicorn.run()` to use `settings.port` instead of hardcoded `8001`
  - Modified `uvicorn.run()` to use all settings dynamically (host, port, reload, log_level)
  - Updated the FastAPI server configuration to use `settings.port` in the development server URL

### 2. Updated default port in settings.py
- **File**: `src/pattern_mining/config/settings.py`
- **Change**: Changed default port from `8000` to `8003`

### 3. Updated example environment file
- **File**: `.env.example`
- **Change**: Changed `API_PORT` from `8000` to `8003`

## Configuration
The service now properly reads the port configuration from:
1. Environment variable: `PORT` or `API_PORT`
2. Settings default: `8003`
3. `.env` file if present

## Verification
To verify the fix:
```bash
# Run the service
python -m pattern_mining.api.main

# Or with custom port
PORT=8003 python -m pattern_mining.api.main

# Check that it's listening on the correct port
curl http://localhost:8003/health
```

## Impact
This fix allows the Pattern Mining service to run on its designated port (8003) alongside other services without port conflicts. The service configuration is now consistent with the docker-compose setup and other service expectations.