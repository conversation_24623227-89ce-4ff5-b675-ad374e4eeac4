# Pattern Mining Service Configuration Template
# SECURITY WARNING: Copy this file to .env and replace ALL values
# Never commit .env files to version control

# ==============================================================================
# CORE CONFIGURATION
# ==============================================================================

# Environment
ENVIRONMENT=development  # production|staging|development

# Service Configuration
SERVICE_NAME=pattern-mining
SERVICE_VERSION=2.0.0
LOG_LEVEL=INFO  # DEBUG|INFO|WARNING|ERROR

# API Configuration
API_HOST=0.0.0.0
API_PORT=8003
API_PREFIX=/api/v1
WORKERS=1  # Number of Uvicorn workers

# ==============================================================================
# SECURITY CONFIGURATION - MUST CHANGE ALL VALUES
# ==============================================================================

# JWT Authentication
JWT_SECRET=REPLACE_WITH_SECURE_SECRET_RUN_openssl_rand_base64_32
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Keys - REQUIRED
GEMINI_API_KEY=REPLACE_WITH_YOUR_GEMINI_API_KEY
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# Secret Management
SECRET_KEY=REPLACE_WITH_SECURE_SECRET_RUN_openssl_rand_base64_64
ENCRYPTION_KEY=REPLACE_WITH_SECURE_KEY_RUN_openssl_rand_base64_32

# ==============================================================================
# GOOGLE CLOUD CONFIGURATION
# ==============================================================================

# GCP Project
GCP_PROJECT_ID=your-project-id
GCP_REGION=us-central1

# AI/ML Services
GEMINI_MODEL=gemini-2.5-flash
VERTEX_AI_LOCATION=us-central1
ENABLE_PROMPT_INJECTION_PROTECTION=true
MAX_PROMPT_LENGTH=10000

# ==============================================================================
# DATA STORAGE
# ==============================================================================

# PostgreSQL Database
DATABASE_URL=postgresql://REPLACE_DB_USER:REPLACE_DB_PASSWORD@localhost:5432/pattern_mining
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=40
DATABASE_POOL_TIMEOUT=30

# Redis Cache
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=REPLACE_WITH_SECURE_REDIS_PASSWORD
REDIS_MAX_CONNECTIONS=50
CACHE_TTL_PATTERNS=3600
CACHE_TTL_EMBEDDINGS=86400

# BigQuery
BIGQUERY_DATASET=pattern_mining
BIGQUERY_LOCATION=US
BIGQUERY_TIMEOUT=300

# Spanner
SPANNER_INSTANCE=pattern-mining-prod
SPANNER_DATABASE=pattern-mining

# ==============================================================================
# PROCESSING CONFIGURATION
# ==============================================================================

# Distributed processing has been removed - single instance only
MAX_WORKERS=10
BATCH_SIZE=100
PROCESSING_TIMEOUT=300

# Performance
MAX_CONCURRENT_ANALYSES=50
MEMORY_LIMIT_MB=4096
ENABLE_GPU=false

# ==============================================================================
# SECURITY FEATURES (Phase 1.2 Complete)
# ==============================================================================

# Configuration Access Control
CONFIG_ACCESS_CONTROL_ENABLED=true
CONFIG_AUDIT_RETENTION_DAYS=90
CONFIG_PERMISSION_CACHE_TTL=300
CONFIG_AUDIT_LOG_ENABLED=true
CONFIG_SECURITY_MONITORING_ENABLED=true

# Secret Rotation
SECRET_ROTATION_ENABLED=true
SECRET_ROTATION_INTERVAL_HOURS=24
SECRET_ROTATION_GRACE_PERIOD_HOURS=2

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# ==============================================================================
# MONITORING & OBSERVABILITY
# ==============================================================================

# Metrics
ENABLE_METRICS=true
METRICS_PORT=9090

# Tracing
ENABLE_TRACING=false
TRACE_SAMPLE_RATE=0.1
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# Health Checks
HEALTH_CHECK_INTERVAL=30
READINESS_TIMEOUT=10

# ==============================================================================
# DEVELOPMENT ONLY
# ==============================================================================

# Debug Mode
DEBUG=false
RELOAD=false  # Auto-reload on code changes

# Test Configuration
TEST_MODE=false
MOCK_EXTERNAL_SERVICES=false

# Local Development Overrides
LOCAL_DEV_MODE=false
DISABLE_AUTH=false  # NEVER set to true in production