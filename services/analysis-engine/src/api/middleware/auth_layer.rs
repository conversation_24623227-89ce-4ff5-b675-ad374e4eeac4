use crate::api::errors::{Error<PERSON><PERSON>x<PERSON>, ErrorResponse, ErrorType};
use crate::api::AppState;
use crate::audit::{AuditAction, AuditEventBuilder, AuditLogger, AuditOutcome, AuditSeverity};
use crate::storage::connection_pool::SpannerConnectionManager;
use axum::{
    extract::Request,
    response::{IntoResponse, Response},
};
use base64::{engine::general_purpose, Engine as _};
use bb8::Pool;
use dashmap::DashMap;
use google_cloud_spanner::statement::Statement;
use jsonwebtoken::{decode, decode_header, Algorithm, DecodingKey, Validation};
use prometheus::{
    register_histogram_vec, register_int_counter, register_int_counter_vec, HistogramVec,
    IntCounter, IntCounterVec,
};
use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256};
use std::collections::HashMap;
use std::future::Future;
use std::pin::Pin;
use std::sync::Arc;
use std::task::{Context, Poll};
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tower::{Layer, Service};
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,                // Subject (user ID)
    exp: u64,                   // Expiration time
    iat: u64,                   // Issued at
    aud: String,                // Audience
    iss: String,                // Issuer
    nbf: Option<u64>,           // Not before
    jti: Option<String>,        // JWT ID for revocation tracking
    scope: Option<String>,      // Token scope/permissions
    session_id: Option<String>, // Session identifier for revocation
    device_id: Option<String>,  // Device fingerprint for binding
}

#[derive(Debug)]
struct UserInfo {
    rate_limit: i64,
}

// JWT validation configuration
#[derive(Debug, Clone)]
struct JwtConfig {
    expected_issuer: String,
    max_token_age_seconds: u64,
    max_clock_skew_seconds: u64,
    require_jti: bool,
    require_device_binding: bool,
    allowed_scopes: Vec<String>,
}

impl Default for JwtConfig {
    fn default() -> Self {
        Self {
            expected_issuer: std::env::var("JWT_ISSUER")
                .unwrap_or_else(|_| "ccl-platform".to_string()),
            max_token_age_seconds: std::env::var("JWT_MAX_AGE_SECONDS")
                .unwrap_or_else(|_| "604800".to_string())
                .parse()
                .unwrap_or(604800),
            max_clock_skew_seconds: 300,
            require_jti: std::env::var("JWT_REQUIRE_JTI")
                .unwrap_or_else(|_| "true".to_string())
                .parse()
                .unwrap_or(true),
            require_device_binding: std::env::var("JWT_REQUIRE_DEVICE_BINDING")
                .unwrap_or_else(|_| "false".to_string())
                .parse()
                .unwrap_or(false),
            allowed_scopes: std::env::var("JWT_ALLOWED_SCOPES")
                .unwrap_or_else(|_| "analysis:read,analysis:write".to_string())
                .split(',')
                .map(|s| s.trim().to_string())
                .collect(),
        }
    }
}

// In-memory rate limiting fallback
lazy_static::lazy_static! {
    static ref RATE_LIMIT_CACHE: DashMap<String, (i64, SystemTime)> = DashMap::new();
}

// Token revocation store
lazy_static::lazy_static! {
    static ref REVOKED_TOKENS: DashMap<String, SystemTime> = DashMap::new();
    static ref REVOKED_SESSIONS: DashMap<String, SystemTime> = DashMap::new();
}

// JWT key management for rotation
#[derive(Clone)]
struct JwtKey {
    kid: String,                    // Key ID
    key: DecodingKey,               // The actual key for verification
    algorithm: Algorithm,           // Algorithm used with this key
    expires_at: Option<SystemTime>, // When this key expires
}

impl std::fmt::Debug for JwtKey {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("JwtKey")
            .field("kid", &self.kid)
            .field("algorithm", &self.algorithm)
            .field("expires_at", &self.expires_at)
            .field("key", &"[REDACTED]")
            .finish()
    }
}

#[derive(Debug)]
struct KeyManager {
    keys: HashMap<String, JwtKey>,
    default_key_id: Option<String>,
}

impl KeyManager {
    fn new() -> Self {
        Self {
            keys: HashMap::new(),
            default_key_id: None,
        }
    }

    fn add_key(
        &mut self,
        kid: String,
        key: DecodingKey,
        algorithm: Algorithm,
        expires_at: Option<SystemTime>,
    ) {
        let jwt_key = JwtKey {
            kid: kid.clone(),
            key,
            algorithm,
            expires_at,
        };
        self.keys.insert(kid.clone(), jwt_key);

        // Set as default if it's the first key
        if self.default_key_id.is_none() {
            self.default_key_id = Some(kid);
        }
    }

    fn get_key(&self, kid: &str) -> Option<&JwtKey> {
        self.keys.get(kid)
    }

    fn get_default_key(&self) -> Option<&JwtKey> {
        self.default_key_id
            .as_ref()
            .and_then(|kid| self.keys.get(kid))
    }

    fn cleanup_expired_keys(&mut self) {
        let now = SystemTime::now();
        self.keys
            .retain(|_, key| key.expires_at.is_none_or(|expires| expires > now));
    }
}

// Authentication metrics
lazy_static::lazy_static! {
    static ref AUTH_REQUESTS_TOTAL: IntCounterVec = register_int_counter_vec!(
        "auth_requests_total",
        "Total number of authentication requests",
        &["method", "result"]
    ).expect("Failed to register auth_requests_total metric");

    static ref AUTH_FAILURES_TOTAL: IntCounterVec = register_int_counter_vec!(
        "auth_failures_total",
        "Total number of authentication failures",
        &["method", "reason"]
    ).expect("Failed to register auth_failures_total metric");

    static ref AUTH_DURATION: HistogramVec = register_histogram_vec!(
        "auth_duration_seconds",
        "Authentication request duration in seconds",
        &["method"],
        vec![0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0]
    ).expect("Failed to register auth_duration metric");

    static ref RATE_LIMIT_EXCEEDED_TOTAL: IntCounter = register_int_counter!(
        "rate_limit_exceeded_total",
        "Total number of rate limit exceeded responses"
    ).expect("Failed to register rate_limit_exceeded_total metric");
}

// Global key manager for JWT key rotation
lazy_static::lazy_static! {
    static ref JWT_KEY_MANAGER: Arc<tokio::sync::RwLock<KeyManager>> = {
        let mut manager = KeyManager::new();

        // Initialize with default key from environment
        if let Ok(jwt_secret) = std::env::var("JWT_SECRET") {
            let key = DecodingKey::from_secret(jwt_secret.as_bytes());
            manager.add_key("default".to_string(), key, Algorithm::HS256, None);
        }

        // Load additional keys from environment if available
        load_additional_keys(&mut manager);

        Arc::new(tokio::sync::RwLock::new(manager))
    };
}

fn load_additional_keys(manager: &mut KeyManager) {
    // Load keys from environment variables with pattern JWT_KEY_{KID}
    for (key, value) in std::env::vars() {
        if key.starts_with("JWT_KEY_") {
            if let Some(kid_suffix) = key.strip_prefix("JWT_KEY_") {
                let kid = kid_suffix.to_lowercase();
                let decoding_key = DecodingKey::from_secret(value.as_bytes());
                tracing::info!("Loaded JWT key with ID: {}", kid);
                manager.add_key(kid, decoding_key, Algorithm::HS256, None);
            }
        }
    }
}

// Public function to refresh JWT keys (call periodically)
pub async fn refresh_jwt_keys() -> Result<(), String> {
    let mut manager = JWT_KEY_MANAGER.write().await;

    // Clean up expired keys first
    manager.cleanup_expired_keys();

    // Reload from environment variables
    load_additional_keys(&mut manager);

    tracing::info!("JWT keys refreshed successfully");
    Ok(())
}

#[derive(Clone)]
pub struct AuthLayer {
    state: AppState,
}

impl AuthLayer {
    pub fn new(state: AppState) -> Self {
        Self { state }
    }
}

impl<S> Layer<S> for AuthLayer {
    type Service = AuthService<S>;

    fn layer(&self, inner: S) -> Self::Service {
        AuthService {
            inner,
            state: self.state.clone(),
        }
    }
}

#[derive(Clone)]
pub struct AuthService<S> {
    inner: S,
    state: AppState,
}

impl<S> Service<Request> for AuthService<S>
where
    S: Service<Request, Response = Response> + Send + Clone + 'static,
    S::Future: Send + 'static,
    S::Error: Into<std::convert::Infallible>,
{
    type Response = S::Response;
    type Error = std::convert::Infallible;
    type Future = Pin<Box<dyn Future<Output = Result<Self::Response, Self::Error>> + Send>>;

    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx).map_err(Into::into)
    }

    fn call(&mut self, mut request: Request) -> Self::Future {
        // Clone necessary data for the async block
        let state = self.state.clone();

        // We need to clone the inner service to move it into the async block
        let mut inner = self.inner.clone();

        Box::pin(async move {
            // Allow public endpoints without auth
            let path = request.uri().path();
            if path == "/health"
                || path == "/health/live"
                || path == "/health/auth"
                || path == "/health/detailed"
                || path == "/ready"
                || path == "/metrics"
                || path == "/backpressure"
                || path == "/circuit-breakers"
            {
                let response = inner.call(request).await.map_err(Into::into)?;
                return Ok(response);
            }

            // Start timing authentication
            let auth_start = Instant::now();

            // Extract headers
            let headers = request.headers();

            // Check for API key
            let api_key = headers.get("x-api-key").and_then(|v| v.to_str().ok());

            // Check for Bearer token
            let auth_header = headers.get("authorization").and_then(|v| v.to_str().ok());

            // Determine auth method for metrics
            let auth_method = match (&api_key, &auth_header) {
                (Some(_), _) => "api_key",
                (_, Some(header)) if header.starts_with("Bearer ") => "jwt",
                _ => "none",
            };

            let auth_result = match (api_key, auth_header) {
                (Some(key), _) => match &state.spanner_pool {
                    Some(pool) => validate_api_key(key, pool).await,
                    None => {
                        tracing::warn!("API key validation skipped - database unavailable");
                        Ok(("memory-user".to_string(), 100))
                    }
                },
                (_, Some(header)) if header.starts_with("Bearer ") => {
                    let token = &header[7..];
                    let device_fingerprint = generate_device_fingerprint(&request);
                    validate_jwt_token_with_request(token, &state, Some(device_fingerprint)).await
                }
                _ => Err("No authentication provided".to_string()),
            };

            // Record authentication duration
            let auth_duration = auth_start.elapsed().as_secs_f64();
            AUTH_DURATION
                .with_label_values(&[auth_method])
                .observe(auth_duration);

            match auth_result {
                Ok((user_id, rate_limit)) => {
                    // Check rate limiting
                    let rate_limit_result = check_rate_limit(&state, &user_id, rate_limit).await;

                    match rate_limit_result {
                        Ok((allowed, remaining, reset_time)) => {
                            if !allowed {
                                // Increment rate limit exceeded counter
                                RATE_LIMIT_EXCEEDED_TOTAL.inc();
                                AUTH_REQUESTS_TOTAL
                                    .with_label_values(&[auth_method, "rate_limited"])
                                    .inc();

                                return Ok(create_rate_limit_error_response(
                                    remaining, reset_time, rate_limit, &request,
                                ));
                            }

                            // Add user ID to request extensions for downstream handlers
                            request.extensions_mut().insert(user_id.clone());

                            // Log successful authentication
                            tracing::debug!("Authentication successful for user: {}", user_id);

                            // Record successful authentication metric
                            AUTH_REQUESTS_TOTAL
                                .with_label_values(&[auth_method, "success"])
                                .inc();

                            // Audit log successful authentication
                            let audit_logger = AuditLogger::new(state.spanner_pool.clone());
                            let client_ip = extract_client_ip(&request);
                            let user_agent = extract_user_agent(&request);

                            let audit_event = AuditEventBuilder::new(AuditAction::LoginSuccess)
                                .user_id(user_id.clone())
                                .ip_address(client_ip.unwrap_or_else(|| "unknown".to_string()))
                                .user_agent(user_agent.unwrap_or_else(|| "unknown".to_string()))
                                .outcome(AuditOutcome::Success)
                                .severity(AuditSeverity::Info)
                                .metadata(serde_json::json!({
                                    "auth_method": if request.headers().contains_key("authorization") { "jwt" } else { "api_key" },
                                    "rate_limit": rate_limit
                                }))
                                .build();

                            if let Err(e) = audit_logger.log_event(audit_event).await {
                                tracing::error!("Failed to log audit event: {}", e);
                            }

                            // Continue with the request
                            let mut response = inner.call(request).await.map_err(Into::into)?;

                            // Add rate limit headers to the response
                            let headers = response.headers_mut();
                            if let Ok(limit_header) = rate_limit.to_string().parse() {
                                headers.insert("X-RateLimit-Limit", limit_header);
                            }
                            if let Ok(remaining_header) = remaining.to_string().parse() {
                                headers.insert("X-RateLimit-Remaining", remaining_header);
                            }
                            if let Ok(reset_header) = reset_time.to_string().parse() {
                                headers.insert("X-RateLimit-Reset", reset_header);
                            }

                            Ok(response)
                        }
                        Err(e) => {
                            tracing::error!("Rate limiting check failed: {}", e);
                            // If rate limiting fails, allow the request but log the error
                            request.extensions_mut().insert(user_id);
                            let response = inner.call(request).await.map_err(Into::into)?;
                            Ok(response)
                        }
                    }
                }
                Err(error) => {
                    // Log authentication failure
                    tracing::warn!("Authentication failed: {}", error);

                    // Record authentication failure metric
                    AUTH_REQUESTS_TOTAL
                        .with_label_values(&[auth_method, "failure"])
                        .inc();

                    // Audit log authentication failure
                    let audit_logger = AuditLogger::new(state.spanner_pool.clone());
                    let client_ip = extract_client_ip(&request);
                    let user_agent = extract_user_agent(&request);

                    let audit_event = AuditEventBuilder::new(AuditAction::LoginFailure)
                        .ip_address(client_ip.unwrap_or_else(|| "unknown".to_string()))
                        .user_agent(user_agent.unwrap_or_else(|| "unknown".to_string()))
                        .outcome(AuditOutcome::Failure)
                        .severity(AuditSeverity::Warning)
                        .metadata(serde_json::json!({
                            "error": error,
                            "auth_method": if request.headers().contains_key("authorization") { "jwt" } else { "api_key" }
                        }))
                        .build();

                    if let Err(e) = audit_logger.log_event(audit_event).await {
                        tracing::error!("Failed to log audit event: {}", e);
                    }

                    // Return authentication error
                    Ok(create_auth_error_response(&error, &request))
                }
            }
        })
    }
}

// Import all the security functions from auth.rs
// These are production-ready implementations with proper security

async fn validate_api_key(
    key: &str,
    spanner_pool: &Pool<SpannerConnectionManager>,
) -> Result<(String, i64), String> {
    // Get a connection from the pool
    let spanner = spanner_pool
        .get()
        .await
        .map_err(|e| format!("Failed to get Spanner connection from pool: {e:?}"))?;

    // Extract key prefix for efficient lookup (first 8 characters after "ak_" prefix)
    let key_prefix = if key.len() >= 11 && key.starts_with("ak_") {
        key[3..11].to_string() // 8 chars after "ak_"
    } else {
        return Err("Invalid API key format".to_string());
    };

    // Query by key prefix for efficient lookup
    // This reduces the search space from O(n) to O(1) average case
    let mut statement = Statement::new(
        "SELECT user_id, rate_limit, expires_at, is_active, key_hash, salt
         FROM api_keys
         WHERE is_active = true AND key_prefix = @key_prefix",
    );
    statement.add_param("key_prefix", &key_prefix);

    // Use a read-only transaction from the pooled connection
    let mut tx = spanner
        .read_only_transaction()
        .await
        .map_err(|e| format!("Failed to create read transaction: {e}"))?;
    let mut reader = tx
        .query(statement)
        .await
        .map_err(|e| format!("Failed to query API keys: {e}"))?;

    // In most cases, there should only be one key with a given prefix
    // But we still iterate in case of (extremely unlikely) collisions
    while let Some(row) = reader
        .next()
        .await
        .map_err(|e| format!("Failed to read row: {e}"))?
    {
        let stored_hash: String = row
            .column_by_name("key_hash")
            .map_err(|e| format!("Failed to read key_hash: {e}"))?;
        let salt: String = row
            .column_by_name("salt")
            .map_err(|e| format!("Failed to read salt: {e}"))?;

        // Verify the full API key against the stored hash
        match verify_api_key(key, &stored_hash, &salt) {
            Ok(true) => {
                // Check if key has expired
                let expires_at: Result<String, _> = row.column_by_name("expires_at");
                if let Ok(expires_str) = expires_at {
                    let expires_time = chrono::DateTime::parse_from_rfc3339(&expires_str)
                        .map_err(|e| format!("Invalid expiration time format: {e}"))?;
                    if expires_time < chrono::Utc::now() {
                        return Err("API key has expired".to_string());
                    }
                }

                let user_id: String = row
                    .column_by_name("user_id")
                    .map_err(|e| format!("Failed to read user_id: {e}"))?;
                let rate_limit: i64 = row
                    .column_by_name("rate_limit")
                    .map_err(|e| format!("Failed to read rate_limit: {e}"))?;

                tracing::debug!(
                    "API key validated for user: {}, rate_limit: {}",
                    user_id,
                    rate_limit
                );
                return Ok((user_id, rate_limit));
            }
            Ok(false) => {
                // Key prefix matched but full key verification failed
                // This is a collision - continue to check other keys with same prefix
                continue;
            }
            Err(e) => {
                tracing::warn!("Error verifying API key: {}", e);
                continue;
            }
        }
    }

    Err("Invalid API key".to_string())
}

fn verify_api_key(api_key: &str, stored_hash: &str, salt: &str) -> Result<bool, String> {
    let computed_hash = hash_api_key_with_salt(api_key, salt)?;
    Ok(computed_hash == stored_hash)
}

fn hash_api_key_with_salt(api_key: &str, salt: &str) -> Result<String, String> {
    let salt_bytes = general_purpose::STANDARD
        .decode(salt)
        .map_err(|e| format!("Failed to decode salt: {e}"))?;

    let iterations = 100_000;
    let mut hash = api_key.as_bytes().to_vec();

    for _ in 0..iterations {
        let mut hasher = Sha256::new();
        hasher.update(&hash);
        hasher.update(&salt_bytes);
        hash = hasher.finalize().to_vec();
    }

    Ok(general_purpose::STANDARD.encode(hash))
}

// Generate a new API key with proper format and prefix for efficient lookup
pub fn generate_api_key() -> Result<(String, String, String, String), String> {
    use rand::{thread_rng, Rng};

    // Generate a random API key
    let mut rng = thread_rng();
    let key_bytes: [u8; 32] = rng.gen();
    let api_key = format!("ak_{}", general_purpose::STANDARD.encode(key_bytes));

    // Extract prefix for indexing (first 8 characters after "ak_" prefix)
    // This allows efficient database lookups without exposing the full key
    let key_prefix = if api_key.len() >= 11 {
        api_key[3..11].to_string()
    } else {
        return Err("Generated API key too short".to_string());
    };

    // Generate salt and hash
    let salt = generate_salt();
    let hash = hash_api_key_with_salt(&api_key, &salt)?;

    // Return: (api_key, hash, salt, key_prefix)
    Ok((api_key, hash, salt, key_prefix))
}

fn generate_salt() -> String {
    use rand::{thread_rng, Rng};
    let mut rng = thread_rng();
    let salt_bytes: [u8; 32] = rng.gen();
    general_purpose::STANDARD.encode(salt_bytes)
}

async fn validate_jwt_token_with_request(
    token: &str,
    state: &AppState,
    device_fingerprint: Option<String>,
) -> Result<(String, i64), String> {
    let config = JwtConfig::default();

    // Decode header to determine key ID if using key rotation
    let header = decode_header(token).map_err(|e| format!("Invalid JWT header: {e}"))?;

    // Get the appropriate key for verification based on key ID
    let key_manager = JWT_KEY_MANAGER.read().await;

    let jwt_key = if let Some(kid) = header.kid {
        // Use specific key if key ID is provided
        key_manager
            .get_key(&kid)
            .ok_or_else(|| format!("Unknown key ID: {kid}"))?
    } else {
        // Fall back to default key if no key ID specified
        key_manager
            .get_default_key()
            .ok_or_else(|| "No default JWT key configured - ensure JWT_SECRET is set".to_string())?
    };

    // Check if key has expired
    if let Some(expires_at) = jwt_key.expires_at {
        if SystemTime::now() > expires_at {
            return Err("JWT key has expired".to_string());
        }
    }

    // Configure validation with the key's algorithm
    let mut validation = Validation::new(jwt_key.algorithm);
    let expected_aud =
        std::env::var("JWT_AUDIENCE").unwrap_or_else(|_| "ccl-analysis-engine".to_string());
    validation.set_audience(&[&expected_aud]);
    validation.set_issuer(&[&config.expected_issuer]);
    validation.validate_exp = true;
    validation.validate_nbf = true;
    validation.validate_aud = true;
    validation.leeway = config.max_clock_skew_seconds;

    let token_data =
        decode::<Claims>(token, &jwt_key.key, &validation).map_err(|e| match e.kind() {
            jsonwebtoken::errors::ErrorKind::ExpiredSignature => "Token has expired".to_string(),
            jsonwebtoken::errors::ErrorKind::InvalidToken => "Invalid token format".to_string(),
            jsonwebtoken::errors::ErrorKind::InvalidAudience => {
                "Invalid token audience".to_string()
            }
            jsonwebtoken::errors::ErrorKind::InvalidIssuer => "Invalid token issuer".to_string(),
            jsonwebtoken::errors::ErrorKind::InvalidSignature => {
                "Invalid token signature".to_string()
            }
            jsonwebtoken::errors::ErrorKind::ImmatureSignature => "Token not yet valid".to_string(),
            _ => format!("Token validation failed: {e}"),
        })?;

    let claims = &token_data.claims;

    validate_token_claims(claims, &config)?;

    if let Some(jti) = &claims.jti {
        if is_token_revoked(jti) {
            return Err("Token has been revoked".to_string());
        }
    } else if config.require_jti {
        return Err("Token missing required JWT ID".to_string());
    }

    if let Some(session_id) = &claims.session_id {
        if is_session_revoked(session_id) {
            return Err("Session has been revoked".to_string());
        }
    }

    if let Some(fingerprint) = device_fingerprint {
        validate_device_binding_with_fingerprint(&fingerprint, claims)?;
    }

    let user_id = claims.sub.clone();
    let user_info = validate_user_in_database(&user_id, state).await?;

    tracing::debug!(
        "JWT validated for user: {} with rate limit: {}",
        user_id,
        user_info.rate_limit
    );

    Ok((user_id, user_info.rate_limit))
}

fn validate_token_claims(claims: &Claims, config: &JwtConfig) -> Result<(), String> {
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .map_err(|e| format!("System time error: {e}"))?
        .as_secs();

    if now - claims.iat > config.max_token_age_seconds {
        return Err("Token is too old".to_string());
    }

    if claims.iss != config.expected_issuer {
        return Err("Invalid token issuer".to_string());
    }

    if let Some(nbf) = claims.nbf {
        if now + config.max_clock_skew_seconds < nbf {
            return Err("Token not yet valid".to_string());
        }
    }

    if let Some(scope) = &claims.scope {
        let token_scopes: Vec<&str> = scope.split(' ').collect();
        let has_valid_scope = token_scopes
            .iter()
            .any(|s| config.allowed_scopes.contains(&s.to_string()));

        if !has_valid_scope {
            return Err("Token has insufficient scope".to_string());
        }
    }

    if config.require_device_binding && claims.device_id.is_none() {
        return Err("Token missing required device binding".to_string());
    }

    Ok(())
}

async fn validate_user_in_database(user_id: &str, state: &AppState) -> Result<UserInfo, String> {
    let mut statement = Statement::new(
        "SELECT subscription_tier, rate_limit, is_active, created_at
         FROM users
         WHERE user_id = @user_id",
    );
    statement.add_param("user_id", &user_id);

    let row = match &state.spanner_pool {
        Some(pool) => {
            let spanner = pool
                .get()
                .await
                .map_err(|e| format!("Failed to get spanner connection: {e:?}"))?;
            // The Spanner client maintains connection pooling internally
            let mut tx = spanner
                .read_only_transaction()
                .await
                .map_err(|e| format!("Failed to create read transaction: {e}"))?;
            let mut reader = tx
                .query(statement)
                .await
                .map_err(|e| format!("Failed to query user: {e}"))?;
            reader
                .next()
                .await
                .map_err(|e| format!("Failed to read row: {e}"))?
        }
        None => {
            return Ok(UserInfo { rate_limit: 100 });
        }
    };

    if let Some(row) = row {
        let rate_limit: i64 = row
            .column_by_name("rate_limit")
            .map_err(|e| format!("Failed to read rate_limit: {e}"))?;

        Ok(UserInfo { rate_limit })
    } else {
        Err("User not found".to_string())
    }
}

fn is_token_revoked(jti: &str) -> bool {
    REVOKED_TOKENS.contains_key(jti)
}

fn is_session_revoked(session_id: &str) -> bool {
    REVOKED_SESSIONS.contains_key(session_id)
}

fn validate_device_binding_with_fingerprint(
    fingerprint: &str,
    claims: &Claims,
) -> Result<(), String> {
    if let Some(token_device_id) = &claims.device_id {
        if token_device_id != fingerprint {
            return Err("Token device binding validation failed".to_string());
        }
    }

    Ok(())
}

fn generate_device_fingerprint(request: &Request) -> String {
    let mut hasher = Sha256::new();

    if let Some(ua) = extract_user_agent(request) {
        hasher.update(ua.as_bytes());
    }

    if let Some(lang) = request
        .headers()
        .get("accept-language")
        .and_then(|h| h.to_str().ok())
    {
        hasher.update(lang.as_bytes());
    }

    if let Some(encoding) = request
        .headers()
        .get("accept-encoding")
        .and_then(|h| h.to_str().ok())
    {
        hasher.update(encoding.as_bytes());
    }

    if let Some(forwarded) = request
        .headers()
        .get("x-forwarded-for")
        .and_then(|h| h.to_str().ok())
    {
        if let Some(first_ip) = forwarded.split(',').next() {
            hasher.update(first_ip.trim().as_bytes());
        }
    }

    if let Some(device_id) = request
        .headers()
        .get("x-device-id")
        .and_then(|h| h.to_str().ok())
    {
        hasher.update(device_id.as_bytes());
    }

    let result = hasher.finalize();
    format!("{result:x}")
}

async fn check_rate_limit(
    state: &AppState,
    user_id: &str,
    limit: i64,
) -> Result<(bool, i64, u64), anyhow::Error> {
    const WINDOW_SECONDS: u64 = 3600; // 1 hour window

    // Try Redis first
    if let Some(pool) = &state.redis_pool {
        let mut redis = pool
            .get()
            .await
            .map_err(|e| anyhow::anyhow!("Failed to get redis connection: {e}"))?;
        match check_redis_rate_limit_direct(&mut redis, user_id, limit, WINDOW_SECONDS).await {
            Ok(result) => return Ok(result),
            Err(e) => {
                tracing::warn!(
                    "Redis rate limiting failed, falling back to in-memory: {}",
                    e
                );
            }
        }
    }

    // Fallback to in-memory rate limiting
    let now = SystemTime::now();

    let mut entry = RATE_LIMIT_CACHE
        .entry(user_id.to_string())
        .or_insert((0, now));
    let (count, last_reset) = entry.value_mut();

    // Check if we need to reset the window
    let elapsed = now
        .duration_since(*last_reset)
        .map_err(|e| anyhow::anyhow!("Time calculation error: {e}"))?
        .as_secs();
    if elapsed >= WINDOW_SECONDS {
        *count = 0;
        *last_reset = now;
    }

    if *count < limit {
        *count += 1;
        let remaining = limit - *count;
        let reset_time = last_reset
            .duration_since(UNIX_EPOCH)
            .map_err(|e| anyhow::anyhow!("Time calculation error: {e}"))?
            .as_secs()
            + WINDOW_SECONDS;
        Ok((true, remaining, reset_time))
    } else {
        let reset_time = last_reset
            .duration_since(UNIX_EPOCH)
            .map_err(|e| anyhow::anyhow!("Time calculation error: {e}"))?
            .as_secs()
            + WINDOW_SECONDS;
        Ok((false, 0, reset_time))
    }
}

fn extract_client_ip(request: &Request) -> Option<String> {
    if let Some(forwarded_for) = request.headers().get("x-forwarded-for") {
        if let Ok(forwarded_str) = forwarded_for.to_str() {
            if let Some(first_ip) = forwarded_str.split(',').next() {
                return Some(first_ip.trim().to_string());
            }
        }
    }

    if let Some(real_ip) = request.headers().get("x-real-ip") {
        if let Ok(ip_str) = real_ip.to_str() {
            return Some(ip_str.to_string());
        }
    }

    if let Some(cf_ip) = request.headers().get("cf-connecting-ip") {
        if let Ok(ip_str) = cf_ip.to_str() {
            return Some(ip_str.to_string());
        }
    }

    None
}

fn extract_user_agent(request: &Request) -> Option<String> {
    request
        .headers()
        .get("user-agent")
        .and_then(|ua| ua.to_str().ok())
        .map(|s| s.to_string())
}

fn create_auth_error_response(error: &str, request: &Request) -> Response {
    let correlation_id = Uuid::new_v4().to_string();

    let context = ErrorContext {
        request_id: Some(correlation_id.clone()),
        user_id: None,
        organization_id: None,
        resource_id: None,
        operation: Some(format!("{} {}", request.method(), request.uri().path())),
        parameters: None,
    };

    let (error_code, user_message) = match error {
        "No authentication provided" => (
            "MISSING_AUTHENTICATION",
            Some("Authentication is required. Please provide an API key or JWT token.".to_string()),
        ),
        e if e.contains("Invalid API key") => (
            "INVALID_API_KEY",
            Some("The provided API key is invalid or has expired.".to_string()),
        ),
        e if e.contains("API key has expired") => (
            "EXPIRED_API_KEY",
            Some("Your API key has expired. Please obtain a new API key.".to_string()),
        ),
        e if e.contains("Token") => (
            "INVALID_JWT_TOKEN",
            Some("The provided JWT token is invalid or has expired.".to_string()),
        ),
        e if e.contains("User not found") => (
            "USER_NOT_FOUND",
            Some("The authenticated user account was not found.".to_string()),
        ),
        e if e.contains("User is not active") || e.contains("User account is inactive") => (
            "USER_INACTIVE",
            Some("Your user account is inactive. Please contact support.".to_string()),
        ),
        _ => (
            "AUTHENTICATION_FAILED",
            Some("Authentication failed. Please check your credentials.".to_string()),
        ),
    };

    let mut error_response = ErrorResponse::new(ErrorType::Authentication, error.to_string());
    error_response.error_code = Some(error_code.to_string());
    error_response.user_message = user_message;
    error_response.correlation_id = Some(correlation_id);
    error_response.context = Some(context);
    error_response.retryable = false;

    match error_code {
        "MISSING_AUTHENTICATION" => {
            error_response.suggestions.push(crate::api::errors::ErrorSuggestion {
                action: "provide_authentication".to_string(),
                description: "Include either an API key in the 'x-api-key' header or a JWT token in the 'Authorization: Bearer <token>' header".to_string(),
                url: Some("https://docs.ccl.dev/authentication".to_string()),
                priority: "high".to_string(),
            });
        }
        "INVALID_API_KEY" => {
            error_response
                .suggestions
                .push(crate::api::errors::ErrorSuggestion {
                    action: "check_api_key".to_string(),
                    description: "Verify your API key is correct and has not expired".to_string(),
                    url: Some("https://docs.ccl.dev/api-keys".to_string()),
                    priority: "high".to_string(),
                });
        }
        "INVALID_JWT_TOKEN" => {
            error_response
                .suggestions
                .push(crate::api::errors::ErrorSuggestion {
                    action: "refresh_token".to_string(),
                    description:
                        "Your JWT token may be expired or invalid. Please obtain a new token"
                            .to_string(),
                    url: Some("https://docs.ccl.dev/jwt-tokens".to_string()),
                    priority: "high".to_string(),
                });
        }
        _ => {}
    }

    let mut response = error_response.into_response();

    // Add security headers
    let headers = response.headers_mut();
    if let Ok(value) = "nosniff".parse() {
        headers.insert("X-Content-Type-Options", value);
    }
    if let Ok(value) = "DENY".parse() {
        headers.insert("X-Frame-Options", value);
    }
    if let Ok(value) = "1; mode=block".parse() {
        headers.insert("X-XSS-Protection", value);
    }
    if let Ok(value) = "no-store, no-cache, must-revalidate".parse() {
        headers.insert("Cache-Control", value);
    }
    if let Ok(value) = "no-cache".parse() {
        headers.insert("Pragma", value);
    }

    response
}

fn create_rate_limit_error_response(
    remaining: i64,
    reset_time: u64,
    rate_limit: i64,
    request: &Request,
) -> Response {
    let correlation_id = Uuid::new_v4().to_string();

    let context = ErrorContext {
        request_id: Some(correlation_id.clone()),
        user_id: None,
        organization_id: None,
        resource_id: None,
        operation: Some(format!("{} {}", request.method(), request.uri().path())),
        parameters: None,
    };

    let mut error = ErrorResponse::new(ErrorType::RateLimit, "Rate limit exceeded".to_string());
    error.error_code = Some("RATE_LIMIT_EXCEEDED".to_string());
    error.user_message = Some(
        "You have exceeded the rate limit. Please wait before making more requests.".to_string(),
    );
    error.correlation_id = Some(correlation_id);
    error.context = Some(context);
    error.retryable = true;

    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or(Duration::from_secs(0))
        .as_secs();
    let retry_after = if reset_time > now {
        (reset_time - now) as u32
    } else {
        60
    };
    error.retry_after_seconds = Some(retry_after);

    error.suggestions.push(crate::api::errors::ErrorSuggestion {
        action: "wait_and_retry".to_string(),
        description: format!("Wait {retry_after} seconds before making another request"),
        url: Some("https://docs.ccl.dev/rate-limits".to_string()),
        priority: "medium".to_string(),
    });

    let mut response = error.into_response();

    // Add rate limit headers
    let headers = response.headers_mut();
    if let Ok(limit_header) = rate_limit.to_string().parse() {
        headers.insert("X-RateLimit-Limit", limit_header);
    }
    if let Ok(remaining_header) = remaining.to_string().parse() {
        headers.insert("X-RateLimit-Remaining", remaining_header);
    }
    if let Ok(reset_header) = reset_time.to_string().parse() {
        headers.insert("X-RateLimit-Reset", reset_header);
    }

    // Add security headers
    if let Ok(value) = "nosniff".parse() {
        headers.insert("X-Content-Type-Options", value);
    }
    if let Ok(value) = "DENY".parse() {
        headers.insert("X-Frame-Options", value);
    }
    if let Ok(value) = "1; mode=block".parse() {
        headers.insert("X-XSS-Protection", value);
    }
    if let Ok(value) = "no-store, no-cache, must-revalidate".parse() {
        headers.insert("Cache-Control", value);
    }
    if let Ok(value) = "no-cache".parse() {
        headers.insert("Pragma", value);
    }

    response
}

async fn check_redis_rate_limit_direct(
    conn: &mut redis::aio::MultiplexedConnection,
    user_id: &str,
    limit: i64,
    window_seconds: u64,
) -> Result<(bool, i64, u64), anyhow::Error> {
    use redis::AsyncCommands;
    use std::time::{SystemTime, UNIX_EPOCH};

    let key = format!("rate_limit:{user_id}");
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .map_err(|e| anyhow::anyhow!("System time error: {}", e))?
        .as_secs();

    // Simple sliding window rate limiting
    let current_count: i64 = conn.get(&key).await.unwrap_or(0);

    if current_count >= limit {
        let ttl: i64 = conn.ttl(&key).await.unwrap_or(window_seconds as i64);
        let reset_time = now + ttl as u64;
        return Ok((false, 0, reset_time));
    }

    // Increment counter
    let new_count: i64 = conn.incr(&key, 1).await?;

    // Set expiration if this is the first request
    if new_count == 1 {
        let _: () = conn.expire(&key, window_seconds as i64).await?;
    }

    let remaining = limit - new_count;
    let reset_time = now + window_seconds;

    Ok((true, remaining, reset_time))
}
